import pickle
from datetime import datetime
import os
import tempfile
DB_PATH = "/dev/shm/mydb.sqlite"  # 在 RAM 磁盘中创建 SQLite 数据库
#DB_PATH = os.path.join(tempfile.gettempdir(), "mydb.sqlite")
import sqlite3
from flask import g

def get_db():
    """获取 SQLite 数据库连接"""
    if "db" not in g:
        g.db = sqlite3.connect(DB_PATH, check_same_thread=False)
        g.db.execute("PRAGMA journal_mode=WAL;")  # 启用 WAL
        g.db.execute("""
            CREATE TABLE IF NOT EXISTS model_map (
                gongxu TEXT,
                device_id TEXT,
                model_data BLOB,
                PRIMARY KEY (gongxu, device_id)
            )
        """)
        g.db.commit()
    return g.db

def close_db():
    db = g.pop("db", None)
    if db is not None:
        db.close()

def save_model_to_db(conn, gongxu,device_id, model_data):
    model_data = pickle.dumps(model_data)
    cursor = conn.cursor()
    """保存模型到 SQLite"""
    cursor.execute("INSERT OR REPLACE INTO model_map (gongxu, device_id,model_data) VALUES (?, ?, ?)",
                   (gongxu, device_id, model_data))
    conn.commit()

def load_model_from_db(conn, gongxu,device_id):
    cursor = conn.cursor()
    """从 SQLite 加载模型"""
    cursor.execute("SELECT * FROM model_map WHERE gongxu=? AND device_id=?", (gongxu, device_id))
    row = cursor.fetchone()
    if row is not None:
        model_data = pickle.loads(row[2])
        return row[0],row[1],model_data
    else:
        return None,None,None
def delete_from_db(conn, gongxu, device_id):
    cursor = conn.cursor()
    cursor.execute("DELETE FROM model_map WHERE gongxu=? AND device_id=?", (gongxu, device_id))
    conn.commit()