import datetime
# 加载液口距校准模型
from yekouju_adjust.beta_version.v1.model import YekoujuAdjustModel as YekoujuAdjustModel
yekouju_model = YekoujuAdjustModel('yekouju_adjust/beta_version/v1/model_data/model_yekouju_0418.joblib')

# 液口距校准推送
def yekoujuadjust(request):
    if request.method == 'POST' or request.method == 'GET':
        time1 = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        dev_number = request.json.get('devnumber')
        daoliutong_up = request.json.get('daoliutong_up')
        daoliutong_down = request.json.get('daoliutong_down')
        daoliutong_left = request.json.get('daoliutong_left')
        daoliutong_right = request.json.get('daoliutong_right')
        daoliutong_upleft = request.json.get('daoliutong_upleft')
        daoliutong_upright = request.json.get('daoliutong_upright')
        dingguo_finish_s1 = request.json.get('dingguo_finish_s1')
        dingguo_finish_s2 = request.json.get('dingguo_finish_s2')
        dingguo_finish_yewen = request.json.get('dingguo_finish_yewen')
        dingguo_finish_yekouju = request.json.get('dingguo_finish_yekouju')
        dingguo_finish_guowei = request.json.get('dingguo_finish_guowei')
        data = {
            "daoliutong_up": daoliutong_up,
            "daoliutong_down": daoliutong_down,
            "daoliutong_left": daoliutong_left,
            "daoliutong_right": daoliutong_right,
            "daoliutong_upleft": daoliutong_upleft,
            "daoliutong_upright": daoliutong_upright,
            "dingguo_finish_yewen": dingguo_finish_yewen,
            "dingguo_finish_guowei": dingguo_finish_guowei,
            "dingguo_finish_yekouju": dingguo_finish_yekouju
        }
        results = yekouju_model.predict(data)
        results = round(results, 2)
        if results < -3:
            results = -3
        elif results > 3:
            results = 3
        else:
            results = results
        js = {"guowei_adjust": results, "version": "1.0.2"}

        return js
