import yaml
import math
import pickle
import numpy as np
from datetime import datetime
import pickle
from collections import deque
class ShouweiIntelligentCorrectionModel():
    @classmethod
    def from_path(cls, keycurves_path, config_path):
        m = cls()
        m.load_config(config_path)
        m.valid = m.load_model(keycurves_path)
        if m.valid:
            m.enable_corr()
            return m

    def load_config(self, config_path):
        with open(config_path) as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.enter_power_step = config['enter_step']['power']
        self.power_adjust_step = config['power_adjust_step']
        self.power_adjust_limit = config['power_adjust_limit']
        # self.wait_length = config['wait_length']
        self.diameter_smooth_window = config['diameter_smooth_window']
        self.diameter_adjust_ratio = config['diameter_adjust_ratio']

    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def match_data(self, data_dict, device_no, product_size, hot_zone_size, initial_pull_speed, end_time):
        # 1. 初步过滤出符合设备号、产品尺寸和热场尺寸的项
        matching_items = {k: v for k, v in data_dict.items() if k[0] == device_no and k[1] == product_size and k[2] == hot_zone_size}

        # 如果没有找到符合设备号的匹配项，则根据产品尺寸和热场尺寸过滤
        if not matching_items:
            matching_items = {k: v for k, v in data_dict.items() if k[1] == product_size and k[2] == hot_zone_size}

        # 如果依然没有找到匹配项，根据产品尺寸过滤
        if not matching_items:
            matching_items = {k: v for k, v in data_dict.items() if k[1] == product_size}

        # 2. 从匹配项中选择进收尾时初始拉速在指定范围内的曲线
        speed_filtered_items = {k: v for k, v in matching_items.items() if (initial_pull_speed - 10) <= float(k[3]) <= (initial_pull_speed + 10)}

        # 如果没有找到符合初始拉速范围的项，返回前面匹配项
        if not speed_filtered_items:
            speed_filtered_items = matching_items

        # 3. 在符合初始拉速范围的项中，选择最近一段时间内的曲线（例如近1个月的）
        from datetime import datetime, timedelta
        current_time = datetime.strptime(end_time, "%Y/%m/%d %H:%M:%S")
        one_month_ago = current_time - timedelta(days=60)

        recent_matches = {k: v for k, v in speed_filtered_items.items() if datetime.strptime(k[4], "%Y/%m/%d %H:%M:%S") >= one_month_ago}

        # 如果存在近1个月内的匹配项，使用这些项；否则，使用所有符合初始拉速范围的项
        final_candidates = recent_matches if recent_matches else speed_filtered_items

        # 4. 在最终候选项中，选择晶体增长重量最小的作为最终结果
        final_match = min(final_candidates.items(), key=lambda item: item[0][-1])  # item[0][-1] 是晶体增长重量

        # 返回最终匹配的记录
        return final_match[1]

    def filter_by_furnace_size_model(self, pickle_file, target_furnace_id, target_product_size, target_thermal_model):
        with open(pickle_file, 'rb') as f:
            data_dict = pickle.load(f)
        key_with_furnace = (target_furnace_id, target_product_size, target_thermal_model)
        if key_with_furnace in data_dict:
            return data_dict[key_with_furnace]
        for (furnace_id, product_size, thermal_model), value in data_dict.items():
            if product_size == target_product_size and thermal_model == target_thermal_model:
                print("找到的曲线：", furnace_id)
                return value
        return None

    def get_crystal_lift_by_unit_length(self, filtered_data, target_unit_length):
        for unit_length, crystal_lift in filtered_data:
            if unit_length == target_unit_length:
                return crystal_lift
        return None

    def filter_by_product_size_model(self, pickle_file, target_product_size):
        with open(pickle_file, 'rb') as f:
            data_dict = pickle.load(f)
        key_with_furnace = target_product_size
        if key_with_furnace in data_dict:
            return data_dict[key_with_furnace]
        return None

    def get_correction_factor_by_unit_length(self, filtered_data, target_unit_length):
        for unit_length, factor in filtered_data:
            if unit_length == target_unit_length:
                return factor
        return None

    def setup(self, product_type, field_size, device_id, power, dengjing_diameter, dengjing_weight, target_dia, target_power, config, lasu_keycurves_path, init_lasu, weight_bin_path_11, weight_bin_path_12,weight_bin_path_105):
        update_keys = ['power_adjust_step', 'power_adjust_limit', 'wait_length', 'diameter_smooth_window', 'diameter_adjust_ratio']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])
        vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(product_type)))]
        vs = vs.get(device_id, [v for d in vs.values() for v in d])
        vs1 = [v for v in vs if float(v['field_size']) - float(field_size) == 0]
        v = min(vs1, key=lambda v: abs(v['power'][0] - power))
        self.power = (v['power'] + (power - v['power'][0])) if len(target_power) == 0 else np.array(target_power)
        self.diameters = v['diameter'] if len(target_dia) == 0 else np.array(target_dia)
        self.corr, self.before_diameters, self.real_power, self.last_adjust = 0, [], [], 14
        self.prev_r, self.prev_l, self.prev_m = dengjing_diameter / 2, 0, dengjing_weight

        self.reallasu = 0
        self.weight_change = 0
        self.lasu_keycurves_path, self.device_id , self.product_type, self.field_size = lasu_keycurves_path, device_id, product_type, field_size
        self.init_lasu = init_lasu
        self.origin_lasu = 0
        self.origin_lasus = []
        self.origin_lasus.append((0.0,self.init_lasu))

        self.time = datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        with open(self.lasu_keycurves_path, 'rb') as f:
            self.data_dict = pickle.load(f)
        self.lasu_keys = self.match_data(self.data_dict, self.device_id, self.product_type, self.field_size, self.init_lasu, self.time)
        self.lasu_keys = [(round(float(x[0]), 1), round(float(x[1]), 1)) for x in self.lasu_keys]
        self.gengxin_list = []

        self.weight_changes = []
        self.init_weight_change = 0
        self.baseline_error = 0
        self.baseline_flag = None
        self.history_jilu = []

        # 新版拉速
        self.history_len_weight = {}
        self.offset = [0, 0.1, -0.1, 0.2, -0.2]
        with open(weight_bin_path_11, 'rb') as f:
            self.weight_bin_dict_11 = pickle.load(f)
        with open(weight_bin_path_12, 'rb') as f:
            self.weight_bin_dict_12 = pickle.load(f)
        with open(weight_bin_path_105, 'rb') as f:
            self.weight_bin_dict_105 = pickle.load(f)
        if product_type == '11':
            self.weight_bin_dict = self.weight_bin_dict_11
        elif product_type == '10.5':
            self.weight_bin_dict = self.weight_bin_dict_105
        else:
            self.weight_bin_dict = self.weight_bin_dict_12
        self.lasu_correction = 0
        self.lasu_adjust_step = 0.2
        self.wait_length = 1

        self.latest_weights = deque(maxlen=3)

        self.real_lasu = []
        self.uper_150_flag = False
        self.chang_150 = False
        self.flag_150 = False
        self.baseline_error_150 = None

    def baseline(self):
        return {'shape_baseline': self.diameters.tolist(), 'power_baseline': self.power.tolist()}

    def finish(self):
        pass

    def clear_corr(self):
        self.corr, self.last_adjust = 0, None

    def disable_corr(self):
        self.do_corr = False

    def enable_corr(self):
        self.do_corr = True

    def disable_history_corr(self):
        self.do_history_corr = False

    def enable_history_corr(self):
        self.do_history_corr = True

    def predict(self, l, weight):

        length = round(float(l), 1)
        self.history_len_weight[l] = weight
        self.gengxin_list.append((l, weight))  # 用于更新曲线

        # 用于存储当前最新的三个重量值
        self.latest_weights.append(weight)

        l = int(l)
        if l - self.last_adjust >= self.wait_length:
            # 寻找前15长的三个重量
            matching_weights = [0, 0, 0]
            data_list = [(key, value) for key, value in self.history_len_weight.items()]
            for increment in self.offset:
                adjusted_length = l - 15 + increment
                # 遍历列表查找匹配的单位长度
                for i in range(2, len(data_list)):  # 从索引2开始，确保有前两个索引
                    if data_list[i][0] == adjusted_length:
                        # 找到匹配的单位长度，提取前两个索引的晶体重量值和当前匹配的晶体重量值
                        matching_weights = [data_list[i - 2][1], data_list[i - 1][1], data_list[i][1]]
                        break
                if matching_weights:  # 如果找到了匹配的值，跳出增量调整循环
                    break
            else:
                print("没有匹配到目标单位长度。")

            # 剔除异常值，相邻之间的两个数差值大于0.2
            def remove_outliers(lst, threshold=0.2):
                # 保留第一个元素
                filtered_lst = [lst[0]]
                # 从第二个元素开始检查
                for i in range(1, len(lst)):
                    # 如果当前元素与前一个元素的差值大于阈值，则跳过当前元素
                    if abs(lst[i] - lst[i - 1]) > threshold:
                        continue
                    # 否则，保留当前元素
                    filtered_lst.append(lst[i])
                return filtered_lst

            matching_weights = remove_outliers(matching_weights)

            # 计算前15长的均值
            pre_15_len_weight = round(sum(matching_weights) / len(matching_weights), 2)

            if pre_15_len_weight == 0:  # 预防找不到前15长重量的情况
                pre_15_len_weight = weight

            # 计算当前重量的均值
            latest_weights = list(self.latest_weights)
            latest_weights = remove_outliers(latest_weights)
            real_weight = round(sum(latest_weights) / len(latest_weights), 2)

            # 计算前15长重量改变量
            real_weight_piancha = round(real_weight - pre_15_len_weight, 1)
            standard_weight_piancha = self.weight_bin_dict[l]
            weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

            def limit_range(value):
                return max(-1, min(1, value))

            if self.product_type == "11" or self.product_type == "10.5":
                self.lasu_correction = limit_range((weight_piancha / 0.1) * 0.2)  # 限制在[-1,1]之间

            elif self.product_type == "12":
                if weight_piancha > 0.1:
                    self.lasu_correction = limit_range((weight_piancha / 0.1) * 0.2)  # 限制在[-1,1]之间
                elif weight_piancha == 0.1:
                    self.lasu_correction = limit_range((weight_piancha / 0.1) * 0.1)  # 限制在[-1,1]之间
                else:
                    self.lasu_correction = 0
            else:
                pass
            self.last_adjust = l
            if l > 150 and weight_piancha <= 0:
                self.uper_150_flag = True
            else:
                self.uper_150_flag = False
        try:
            self.origin_lasu = self.get_crystal_lift_by_unit_length(self.lasu_keys, length)
        except:
            print("该长度没有对应晶升值:", length)

        # if self.product_type == "11" or self.product_type == "10.5":
        #     if self.origin_lasu == None:
        #         self.origin_lasu = self.origin_lasus[-1][1]
        #         self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
        #     else:
        #         if self.baseline_flag == None:
        #             self.baseline_error = round(float(self.init_lasu - self.origin_lasu), 1)
        #             self.baseline_flag = True
        #         self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
        #         self.origin_lasus.append((l,self.origin_lasu))
        # else:
        if l <= 150:
            if self.origin_lasu == None:
                self.origin_lasu = self.origin_lasus[-1][1]
                self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                self.real_lasu.append((l, self.reallasu))
            else:
                if self.baseline_flag == None:
                    self.baseline_error = round(float(self.init_lasu - self.origin_lasu), 1)
                    self.baseline_flag = True
                self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                self.origin_lasus.append((l, self.origin_lasu))
                self.real_lasu.append((l, self.reallasu))
        else:
            if self.uper_150_flag:
                self.chang_150 = True
                self.baseline_error_150 = None
                self.flag_150 = True
                self.reallasu = self.real_lasu[-1][1]
                self.origin_lasus.append((l,self.origin_lasu))
                self.real_lasu.append((l, self.reallasu))
            elif not self.chang_150:
                if self.origin_lasu == None:
                    self.origin_lasu = self.origin_lasus[-1][1]
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                    self.real_lasu.append((l, self.reallasu))
                else:
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                    self.origin_lasus.append((l,self.origin_lasu))
                    self.real_lasu.append((l, self.reallasu))
            else:
                if self.flag_150 and self.baseline_error_150 is None:
                    self.flag_150 = False
                    if self.origin_lasu == None:
                        self.origin_lasu = self.origin_lasus[-1][1]
                    self.baseline_error_150 = round(float(self.real_lasu[-1][1] - self.origin_lasu), 1)
                if self.origin_lasu == None:
                    self.origin_lasu = self.origin_lasus[-1][1]
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error_150
                    self.real_lasu.append((l, self.reallasu))
                else:
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error_150
                    self.origin_lasus.append((l,self.origin_lasu))
                    self.real_lasu.append((l, self.reallasu))

        # 历史曲线自增加功能
        if length == 170.0 and (self.product_type == '11' or self.product_type == '10.5')and len(self.data_dict) <= 500:
            all_weight = round(weight - self.prev_m, 1)
            key = (
                self.device_id,
                self.product_type,
                self.field_size,
                self.init_lasu,
                self.time,
                all_weight
            )
            self.data_dict[key] = self.gengxin_list
            output_pickle_path = 'shouwei_lasu_control/beta_version/v1/model_data/shouwei_lasu_correction.bin'  # 项目路径
            with open(output_pickle_path, 'wb') as pickle_file:
                pickle.dump(self.data_dict, pickle_file)

        if length == 210.0 and self.product_type == '12' and len(self.data_dict) <= 500:
            all_weight = round(weight - self.prev_m, 1)
            key = (
                self.device_id,
                self.product_type,
                self.field_size,
                self.init_lasu,
                self.time,
                all_weight
            )
            self.data_dict[key] = self.gengxin_list
            output_pickle_path = 'shouwei_lasu_control/beta_version/v1/model_data/shouwei_lasu_correction.bin'  # 项目路径
            with open(output_pickle_path, 'wb') as pickle_file:
                pickle.dump(self.data_dict, pickle_file)

        # 功率
        if (not self.do_corr) or l >= self.power.size:
            power = self.power[min(int(l), self.power.size - 1)] + (self.corr if self.do_corr else 0)
            return power, self.reallasu

        def compute_diameter(R, dm, dh):
            if R <= 0 or abs(dh) <= 1e-6 or abs(dm) <= 1e-6:
                return None
            p = 2.34 * 1e-6  # kg/mm^3
            try:
                r = (math.sqrt(3 * dm / (p * math.pi * dh) - 0.75 * R * R) - 0.5 * R)
                return r
            except:
                return None

        compute_r = compute_diameter(self.prev_r, weight - self.prev_m, l - self.prev_l)
        if compute_r == None:
            diameter = None
        else:
            diameter = compute_r * 2
        l = int(l)
        if diameter is not None and (self.last_adjust is None or l - self.last_adjust > self.wait_length):
            if abs(self.diameters[l] - diameter) / self.diameters[l] > self.diameter_adjust_ratio:
                self.corr += (1 if diameter > self.diameters[l] else -1) * self.power_adjust_step
                self.corr = np.clip(self.corr, -self.power_adjust_limit, self.power_adjust_limit)
                self.last_adjust = l

        power = self.power[l] + self.corr

        return power, self.reallasu
