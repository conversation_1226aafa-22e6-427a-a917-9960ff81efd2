import redis
import joblib
import pickle
import io
import sys
import os
from gevent import monkey
# 进行猴子补丁，替换阻塞的标准库函数为gevent协程版本
#monkey.patch_all()
class RedisModelManager:
    def __init__(self, redis_host, redis_port, redis_password,redis_db):
        self.r = redis.StrictRedis(host=redis_host, port=redis_port, password=redis_password,db=redis_db)

    def save_model(self, model_name, device_id, model, version):
        model_data = pickle.dumps(model)
        redis_key = f"{model_name}:{device_id}:{version}"
        # 将模型存入 Redis
        self.r.set(redis_key, model_data)

    def load_model(self, model_name, device_id,version):
        """从 Redis 加载模型"""
        model_data = self.r.get(f"{model_name}:{device_id}:{version}")
        model_data = io.BytesIO(model_data)
        model_bytes = model_data.read()
        if model_data:
            model = pickle.loads(model_bytes)
            return model
        else:
            return None  # 如果模型没有在 Redis 中找到，返回 None

    def delete_model(self, model_name, device_id):
        """删除 Redis 中指定设备的所有版本的模型"""
        all_keys = self.r.keys(f"{model_name}:{device_id}:*")  # 获取该设备的所有模型版本
        for key in all_keys:
            self.r.delete(key)  # 删除模型
            print(f"删除模型：{key.decode()}")

    def delete_model_other(self, model_name, device_id):
        """删除 Redis 中该设备的所有其他模型（不包括当前模型）"""
        keys = self.r.keys(f"*:{device_id}:*")  # 获取该设备的所有存储记录
        for key in keys:
            decoded_key = key.decode()
            model_prefix = decoded_key.split(":")[0]  # 获取模型前缀
            gongxu = str(model_name).split('_')[0]
            if gongxu not in model_prefix:
                self.r.delete(decoded_key)
                print(f"删除缓存：{decoded_key}")

    def check_model_exists(self, func_name, device_id, dict_model_key):
        """
        检查 Redis 中是否存在该模型，并返回对应的版本号
        """
        for key, values in dict_model_key.items():
            if func_name in values:
                # 查找符合 `model_name:device_id:*` 格式的 key
                pattern = f"{key}:{device_id}:*"
                matching_keys = self.r.keys(pattern)

                if matching_keys:
                    # 假设只有一个版本，则直接解析版本号
                    full_key = matching_keys[0].decode()  # 取出 key 并解码
                    version = full_key.split(":")[-1]  # 获取版本号
                    return True, version  # 返回存在状态和版本号

                return False, None  # 未找到模型
        return False, None  # 未匹配到函数名
