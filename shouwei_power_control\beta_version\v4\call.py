import datetime
import gevent
from gevent import Greenlet

import DBUtil.share

model_name = 'sw_power'
from shouwei_power_control.beta_version.v3.model import ShouweiGonglvCorrectionModel
def shouwei_realtime_powerfix_setup(request, model_map,r,model_version):
    if request.method == 'POST' or request.method == 'GET':
        product_type = request.json.get('product_type')
        field_size = request.json.get('field_size')
        device_id = request.json.get('device_id')
        power = request.json.get('power')
        weight = request.json.get('weight')
        diameter = request.json.get('diameter')
        target_dia = request.json.get('target_dia', [])
        target_power = request.json.get('target_power', [])
        config = request.json.get('config', None)
        dengjing_after_100_length = request.json.get('dengjing_after_100_length')
        dengjing_after_100_grow_lasu = request.json.get('dengjing_after_100_grow_lasu')
        dengjing_after_100_target_lasu = request.json.get('dengjing_after_100_target_lasu')

        weight_remain = request.json.get('weight_remain', 0)
        
        if config is None or not isinstance(config, dict):
            config = {}

        model_data ={
            'model': ShouweiGonglvCorrectionModel.from_path(
            keycurves_path='shouwei_power_control/beta_version/v3/model_data/keycurves_shouwei.bin',
            config_path='shouwei_power_control/beta_version/v3/model_data/config_shouwei.yaml'
        ),
            'last_loaded': datetime.datetime.now()  # 更新加载时间

        }
        weight_bin_path_105 = 'shouwei_power_control/beta_version/v3/model_data/shouwei_every10_weight_105.bin'
        weight_bin_path_11 = 'shouwei_power_control/beta_version/v3/model_data/shouwei_every10_weight_11.bin'
        weight_bin_path_12 = 'shouwei_power_control/beta_version/v3/model_data/shouwei_every10_weight_12.bin'
        weight_bin_path_11_rate = 'shouwei_power_control/beta_version/v3/model_data/shouwei_every10_weight_11_rate.bin'
        weight_bin_path_105_rate = 'shouwei_power_control/beta_version/v3/model_data/shouwei_every10_weight_105_rate.bin'

        model_data['model'].setup(product_type, field_size, device_id, power, diameter, weight, target_dia, target_power, config, weight_bin_path_11,
                                              weight_bin_path_12, dengjing_after_100_length, dengjing_after_100_grow_lasu,
                                              dengjing_after_100_target_lasu, weight_bin_path_11_rate,
                                              weight_bin_path_105,weight_bin_path_105_rate, weight_remain)
        DBUtil.share.save_model_to_db(model_map,"shouwei",device_id,model_data)
        Greenlet.spawn(r.save_model, model_name, device_id, model_data,model_version)

        Greenlet.spawn(r.delete_model_other, model_name, device_id)
        return model_data['model'].baseline()

def shouwei_realtime_powerfix(request, model_map,r,model_version):
    if request.method == 'POST' or request.method == 'GET':
        time1 = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        device_id = request.json.get('device_id')
        height = request.json.get('height')
        weight = request.json.get('weight')
        data = [[height, weight]]
        _, _, model_data = DBUtil.share.load_model_from_db(model_map, "shouwei", device_id)
        print("当前时间：炉台号", datetime.datetime.now(), device_id, model_name)
        if model_data is None or (datetime.datetime.now() - model_data['last_loaded']).total_seconds() > 180:

            if model_data is None:
                print(f"{device_id} not in model_map['shouwei']")
            else:
                print("超过三分钟")
            print('模型不在内存中，或者已超过180秒，重新加载', model_name, device_id, model_version)
            model = Greenlet.spawn(r.load_model, model_name, device_id,model_version)
            gevent.joinall([model])  # 等待异步任务完成
            model = model.value
            print("redis缓存时间", model['last_loaded'], device_id, model_name)
            if model:
                model_data = {
                    'model': model['model'],
                    'last_loaded': datetime.datetime.now()  # 更新加载时间
                }
            # else:
            #     return {"error": f"Model for device {device_id} not found in Redis."}
        # 获取模型并进行预测
        print("本地缓存时间", model_data['last_loaded'], device_id, model_name)
        power = model_data['model'].predict(height, weight)
        result = [[power]]
        model_data['last_loaded'] = datetime.datetime.now()
        DBUtil.share.save_model_to_db(model_map,"shouwei",device_id,model_data)
        # 异步保存模型
        Greenlet.spawn(r.save_model, model_name, device_id, model_data,model_version)
        return {"power": float(power)}


def shouwei_realtime_powerfix_finish(request, model_map,r,model_version):
    if request.method == 'POST' or request.method == 'GET':
        device_id = request.json.get('device_id')
        end_code = request.json.get('end_code')
        # 获取模型并进行预测
        #gonglv_correction_all = model_data['model'].finish(end_code)
        DBUtil.share.delete_from_db(model_map,"shouwei",device_id)
        Greenlet.spawn(r.delete_model, model_name, device_id)
        #return {"gonglv_correction_all": gonglv_correction_all}
        return {}

