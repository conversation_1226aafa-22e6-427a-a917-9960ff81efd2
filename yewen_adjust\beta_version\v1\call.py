import datetime

#加载液温修正模型
from yewen_adjust.beta_version.v1.model import YewenCorrModel as YewenCorrModel
yewen_model = YewenCorrModel('yewen_adjust/beta_version/v1/model_data/keycurves_yewen_adjust_all.bin')

# 液温修正推送
def tempadjust(request):
    if request.method == 'POST' or request.method == 'GET':
        time1 = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        dev_number = request.json.get('devnumber')
        product_type = request.json.get('product_type')
        field_size = request.json.get('field_size')
        yinjing_power = request.json.get('yinjing_power')
        feed_number = request.json.get('feed_number')
        yinjing_temperature_start = request.json.get('yinjing_temperature_start')
        yinjing_temperature_end = request.json.get('yinjing_temperature_end')
        yinjing_average_lasu = request.json.get('yinjing_average_lasu')
        yinjing_average_fifty_lasu = request.json.get('yinjing_average_fifty_lasu')
        data = {
            "dev_number": dev_number,
            "product_type": product_type,
            'field_size': field_size,
            "yinjing_power": yinjing_power,
            "feed_number": feed_number,
            "yinjing_temperature_start": yinjing_temperature_start,
            "yinjing_temperature_end": yinjing_temperature_end,
            "yinjing_average_lasu": yinjing_average_lasu,
            "yinjing_average_fifty_lasu": yinjing_average_fifty_lasu}
        if field_size == 36 and product_type == 10.5:
            result = yewen_model.predict_tiantong(data)
        else:
            result = yewen_model.predict(data)
        js = {"yewencorr": float(result), "version": "1.0.3"}

        return js
