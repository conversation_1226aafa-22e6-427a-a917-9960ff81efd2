from yekouju_adjust.beta_version.v2.model import YekoujuAdjustModel
import numpy as np

yekouju_model = YekoujuAdjustModel()

def filter_absolute_liquid(recent_data):
    """
    对绝对液口距历史数据进行滤波处理
    使用中位数滤波 + 移动平均的组合滤波算法
    返回None表示无法滤波，应使用原始值
    """
    if not recent_data or len(recent_data) == 0:
        return None

    # 转换为numpy数组并去除无效值
    data_array = np.array(recent_data)
    valid_data = data_array[~np.isnan(data_array)]

    if len(valid_data) == 0:
        return None

    # 如果数据点少于3个，直接返回平均值
    if len(valid_data) < 3:
        return float(np.mean(valid_data))

    # 使用四分位数方法检测和去除异常值（更稳健）
    q1 = np.percentile(valid_data, 25)
    q3 = np.percentile(valid_data, 75)
    iqr = q3 - q1

    # 定义异常值边界（1.5倍IQR规则）
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr

    # 过滤异常值
    filtered_data = valid_data[(valid_data >= lower_bound) & (valid_data <= upper_bound)]

    # 如果过滤后没有数据，使用中位数
    if len(filtered_data) == 0:
        return float(np.median(valid_data))

    # 移动平均：对滤波后的数据取平均值
    return float(np.mean(filtered_data))

def yekoujuadjust(request):
    if request.method == 'POST' or request.method == 'GET':
        daoliutong_up = request.json.get('daoliutong_up')
        daoliutong_down = request.json.get('daoliutong_down')
        daoliutong_left = request.json.get('daoliutong_left')
        daoliutong_right = request.json.get('daoliutong_right')
        daoliutong_upleft = request.json.get('daoliutong_upleft')
        daoliutong_upright = request.json.get('daoliutong_upright')
        dingguo_finish_yewen = request.json.get('dingguo_finish_yewen')
        dingguo_finish_guowei = request.json.get('dingguo_finish_guowei')

        # 获取原始的dingguo_finish_yekouju值
        dingguo_finish_yekouju = request.json.get('dingguo_finish_yekouju')

        # 获取2分钟历史绝对液口距数据并进行滤波处理
        recent_absolute_liquid = request.json.get('recent_absolute_liquid', [])
        absolute_liquid_filter = filter_absolute_liquid(recent_absolute_liquid)

        # 如果滤波成功（返回值不为None），则使用滤波后的值；否则使用原始值
        if absolute_liquid_filter is not None:
            dingguo_finish_yekouju = absolute_liquid_filter
        data = {
            "daoliutong_up": daoliutong_up,
            "daoliutong_down": daoliutong_down,
            "daoliutong_left": daoliutong_left,
            "daoliutong_right": daoliutong_right,
            "daoliutong_upleft": daoliutong_upleft,
            "daoliutong_upright": daoliutong_upright,
            "dingguo_finish_yewen": dingguo_finish_yewen,
            "dingguo_finish_guowei": dingguo_finish_guowei,
            "dingguo_finish_yekouju": dingguo_finish_yekouju
        }

        try:
            results = yekouju_model.predict(data)
            results = round(results, 2)
            if results < -3:
                results = -3
            elif results > 3:
                results = 3
        except Exception as e:
            # 预测失败时设置为0，相当于不做调整
            results = 0

        return {
            "guowei_adjust": results,
            "version": "1.0.2",
            "absolute_liquid_filter": absolute_liquid_filter if absolute_liquid_filter is not None else dingguo_finish_yekouju
        }
