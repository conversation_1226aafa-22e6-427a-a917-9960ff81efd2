#%%
import hashlib
from flask import abort,jsonify
import threading
from flask import Flask, request
from gevent import pywsgi
import importlib

import DBUtil.share
from DBUtil.pgsql import PsycopgConn
import DBUtil.big_data as bd
import datetime
import json
from os.path import exists
import time
import traceback
import os
import re
import logging
import builtins
poolObj = PsycopgConn()
#数据库连接池初始化
# version = "mysql"
# poolObj.init_pool(version)
app = Flask(__name__)
app.logger.info('Finished Start Flask')
from DBUtil.Redis import RedisModelManager
#sqlite共享内存
# 配置日志
logging.basicConfig(level=logging.DEBUG)
# 读取环境变量（默认为开发环境）
ENV = os.getenv("FLASK_ENV", "development")
# 配置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG if ENV == "development" else logging.INFO)  # 生产环境不输出 DEBUG

# 控制台日志输出
console_handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
console_handler.setLevel(logging.DEBUG if ENV == "development" else logging.INFO)
# 添加处理器
logger.addHandler(console_handler)
app.logger = logger  # 让 Flask 也使用这个 logger

# **屏蔽 print 输出**
if ENV == "production":
    builtins.print = lambda *args, **kwargs: None
# 自定义异常处理
@app.errorhandler(Exception)
def handle_exception(error):
    # 异常时关闭连接
    DBUtil.share.close_db()
    # 捕获所有异常并输出详细信息
    if ENV == "development":  # **测试环境：输出详细错误信息**
        app.logger.error(f"Exception: {traceback.format_exc()}")  # 打印完整错误堆栈
        app.logger.error(f"Request Parameters: {request.args}")  # 输出 URL 查询参数
        app.logger.error(f"Request JSON: {request.get_json()}")  # 输出 JSON 数据
        app.logger.error(f"Request Data: {request.data}")  # 输出原始请求数据
    else:  # **生产环境：仅输出基本错误信息**
        app.logger.error(f"Exception: {error}")  # 仅记录异常信息
        app.logger.error(f"Request URL: {request.url}")  # 记录请求 URL
    # 返回响应
    return jsonify({
        'error': str(error),
        'message': 'Something went wrong, please check the logs for more details.'
    }), 500
# 配置接口路径模板，每个接口名对应一个路径，其中包含 {version} 占位符
function_path_map = {
    # 正式接口...
                                    ########### 控温功率智控 ############
    'kongwen_realtime_powerfix_setup': {
        'path': 'kongwen_power_control.official_version.{version}.call.kongwen_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'kongwen_realtime_powerfix': {
        'path': 'kongwen_power_control.official_version.{version}.call.kongwen_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'kongwen_realtime_powerfix_finish': {
        'path': 'kongwen_power_control.official_version.{version}.call.kongwen_realtime_powerfix_finish',
        'requires_model_map': True  # 不需要传递 model_map
    },

                                    ########### 稳温功率智控 ############
    'tiaowen_realtime_powerfix_timeseries_setup': {
        'path': 'wenwen_power_control.official_version.{version}.call.tiaowen_realtime_powerfix_timeseries_setup',
        'requires_model_map': True
    },
    'tiaowen_realtime_powerfix_timeseries': {
        'path': 'wenwen_power_control.official_version.{version}.call.tiaowen_realtime_powerfix_timeseries',
        'requires_model_map': True
    },
    'tiaowen_realtime_powerfix_timeseries_finish': {
        'path': 'wenwen_power_control.official_version.{version}.call.tiaowen_realtime_powerfix_timeseries_finish',
        'requires_model_map': True
    },

                                    ########### 放肩功率智控 ############
    'fangjian_realtime_powerfix_temp_setup': {
        'path': 'fangjian_power_control.official_version.{version}.call.fangjian_realtime_powerfix_temp_setup',
        'requires_model_map': True
    },
    'fangjian_realtime_powerfix_temp': {
        'path': 'fangjian_power_control.official_version.{version}.call.fangjian_realtime_powerfix_temp',
        'requires_model_map': True
    },
    'fangjian_realtime_powerfix_temp_finish': {
        'path': 'fangjian_power_control.official_version.{version}.call.fangjian_realtime_powerfix_temp_finish',
        'requires_model_map': True
    },

                                    ########### 放肩智控 ############
    'fangjian_intelligent_control_setup': {
        'path': 'fangjian_speed_rise_control.official_version.{version}.call.fangjian_intelligent_control_setup',
        'requires_model_map': True
    },
    'fangjian_intelligent_control': {
        'path': 'fangjian_speed_rise_control.official_version.{version}.call.fangjian_intelligent_control',
        'requires_model_map': True
    },
    'fangjian_intelligent_control_finish': {
        'path': 'fangjian_speed_rise_control.official_version.{version}.call.fangjian_intelligent_control_finish',
        'requires_model_map': True
    },

                                  ########### 转肩拉速智控 ############
    'zhuanjian_intelligent_control_setup': {
        'path': 'zhuanjian_speed_control.official_version.{version}.call.zhuanjian_intelligent_control_setup',
        'requires_model_map': True
    },
    'zhuanjian_intelligent_control': {
        'path': 'zhuanjian_speed_control.official_version.{version}.call.zhuanjian_intelligent_control',
        'requires_model_map': True
    },
    'zhuanjian_intelligent_control_finish': {
        'path': 'zhuanjian_speed_control.official_version.{version}.call.zhuanjian_intelligent_control_finish',
        'requires_model_map': True
    },

                                   ########### 等径功率智控正式 ############
    'dengjing2_realtime_powerfix_setup': {
        'path': 'dengjing_power_control.official_version.{version}.call.dengjing2_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'dengjing2_realtime_powerfix': {
        'path': 'dengjing_power_control.official_version.{version}.call.dengjing2_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'dengjing2_realtime_powerfix_finish': {
        'path': 'dengjing_power_control.official_version.{version}.call.dengjing2_realtime_powerfix_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },

                                        ########### 等径功率智控测试 ############
    'dengjing_realtime_powerfix_test_setup': {
        'path': 'dengjing_power_control.beta_version.{version}.call.dengjing_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'dengjing_realtime_powerfix_test': {
        'path': 'dengjing_power_control.beta_version.{version}.call.dengjing_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'dengjing_realtime_powerfix_test_finish': {
        'path': 'dengjing_power_control.beta_version.{version}.call.dengjing_realtime_powerfix_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },
                                   ########### 等径埚升智控正式 ############
    'dengjing_speed_rise_realtime_powerfix_setup': {
        'path': 'dengjing_rise_control.official_version.{version}.call.dengjing_speed_rise_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'dengjing_speed_rise_realtime_powerfix': {
        'path': 'dengjing_rise_control.official_version.{version}.call.dengjing_speed_rise_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'dengjing_speed_rise_realtime_powerfix_finish': {
        'path': 'dengjing_rise_control.official_version.{version}.call.dengjing_speed_rise_realtime_powerfix_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },

                                          ########### 等径埚升智控测试 ############
    'dengjing_speed_rise_realtime_powerfix_test_setup': {
        'path': 'dengjing_rise_control.beta_version.{version}.call.dengjing_speed_rise_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'dengjing_speed_rise_realtime_powerfix_test': {
        'path': 'dengjing_rise_control.beta_version.{version}.call.dengjing_speed_rise_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'dengjing_speed_rise_realtime_powerfix_test_finish': {
        'path': 'dengjing_rise_control.beta_version.{version}.call.dengjing_speed_rise_realtime_powerfix_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },
                                    ########### 收尾拉速智控正式 ############
    'shouwei_intelligent_control_setup': {
        'path': 'shouwei_lasu_control.official_version.{version}.call.shouwei_intelligent_control_setup',
        'requires_model_map': True
    },
    'shouwei_intelligent_control': {
        'path': 'shouwei_lasu_control.official_version.{version}.call.shouwei_intelligent_control',
        'requires_model_map': True  # 需要传递 model_map
    },
    'shouwei_intelligent_control_finish': {
        'path': 'shouwei_lasu_control.official_version.{version}.call.shouwei_intelligent_control_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },
                                        ########### 收尾拉速智控测试 ############
    'shouwei_intelligent_control_test_setup': {
        'path': 'shouwei_lasu_control.beta_version.{version}.call.shouwei_intelligent_control_setup',
        'requires_model_map': True
    },
    'shouwei_intelligent_control_test': {
        'path': 'shouwei_lasu_control.beta_version.{version}.call.shouwei_intelligent_control',
        'requires_model_map': True  # 需要传递 model_map
    },
    'shouwei_intelligent_control_test_finish': {
        'path': 'shouwei_lasu_control.beta_version.{version}.call.shouwei_intelligent_control_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },
                                         ########### 收尾功率智控正式 ############
    'shouwei_realtime_powerfix_setup': {
        'path': 'shouwei_power_control.official_version.{version}.call.shouwei_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'shouwei_realtime_powerfix': {
        'path': 'shouwei_power_control.official_version.{version}.call.shouwei_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'shouwei_realtime_powerfix_finish': {
        'path': 'shouwei_power_control.official_version.{version}.call.shouwei_realtime_powerfix_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },
                                           ########### 收尾功率智控测试 ############
    'shouwei_realtime_powerfix_test_setup': {
        'path': 'shouwei_power_control.beta_version.{version}.call.shouwei_realtime_powerfix_setup',
        'requires_model_map': True
    },
    'shouwei_realtime_powerfix_test': {
        'path': 'shouwei_power_control.beta_version.{version}.call.shouwei_realtime_powerfix',
        'requires_model_map': True  # 需要传递 model_map
    },
    'shouwei_realtime_powerfix_test_finish': {
        'path': 'shouwei_power_control.beta_version.{version}.call.shouwei_realtime_powerfix_finish',
        'requires_model_map': True  # 是否需要传递 model_map
    },
                                  ########进等功率模型#################
    'predict_jindeng':{

        'path': 'jindeng.official_version.{version}.call.jindeng_predict',
        'requires_model_map': False

    },


                                  ########### 引晶功率智控正式 ############
    'yinjingPowerPushOther':{

        'path': 'yinjing_power_control.official_version.{version}.call.yinjingPowerPushOther',
        'requires_model_map': False
    },

                                 ########### 引晶功率智控测试 ############
    'yinjingPowerPushOther_test': {

        'path': 'yinjing_power_control.beta_version.{version}.call.yinjingPowerPushOther',
        'requires_model_map': False
    },
                                  ########### 引晶自学习智控正式 ############
    'yinjing_xunlian': {

        'path': 'yinjing_selflearn.official_version.{version}.call.yinjing_xunlian',
        'requires_model_map': False
    },

                                    ########### 液温校准正式 ############
    'tempadjust': {

        'path': 'yewen_adjust.official_version.{version}.call.tempadjust',
        'requires_model_map': False
    },

                                  ########### 液温校准测试 ############
    'tempadjust_test': {

        'path': 'yewen_adjust.beta_version.{version}.call.tempadjust',
        'requires_model_map': False
    },
                                    ########### 液口距校准正式 ############
    'yekouju_adjust': {

        'path': 'yekouju_adjust.official_version.{version}.call.yekoujuadjust',
        'requires_model_map': False
    },

                                  ########### 液口距校准测试 ############
    'yekouju_adjust_test': {

        'path': 'yekouju_adjust.beta_version.{version}.call.yekoujuadjust',
        'requires_model_map': False
    },
                                 ########### 液温变化规律 ############
    'edgepoint': {

        'path': 'yewen_change_law.official_version.{version}.call.edgepoint',
        'requires_model_map': False
    },
                                ########### 液面径向温度场正式 ############
    'radial_ccd': {

        'path': 'radial_yewen.official_version.{version}.call.radial_ccd',
        'requires_model_map': False
    },
                               ########### 液面径向温度场测试 ############
    'radial_ccd_test': {

        'path': 'radial_yewen.beta_version.{version}.call.radial_ccd',
        'requires_model_map': False
    },
                                ########### sop\belt ############
    'bigdata_std_curve': {

        'path': 'sop_belt.official_version.{version}.call.bigdata_std_curve',
        'requires_model_map': False
    },
                                ########### 等径相关性分析  ############
    'dengjing_corr': {

        'path': 'dengjing_analysis_corr.official_version.{version}.call.dengjing_corr',
        'requires_model_map': False
    },
                               ########### 等径提拉速  ############
    'dengjing_tilasu': {

        'path': 'dengjing_analysis_lasu.official_version.{version}.call.dengjing_tilasu',
        'requires_model_map': False
    },


    # 测试接口...

                                        ########### 控温功率智控 ############
    'kongwen_realtime_powerfix_test_setup': {
        'path': 'kongwen_power_control.beta_version.{version}.call.kongwen_realtime_powerfix_test_setup',
        'requires_model_map': True
    },
    'kongwen_realtime_powerfix_test': {
        'path': 'kongwen_power_control.beta_version.{version}.call.kongwen_realtime_powerfix_test',
        'requires_model_map': True
    },
    'kongwen_realtime_powerfix_test_finish': {
        'path': 'kongwen_power_control.beta_version.{version}.call.kongwen_realtime_powerfix_test_finish',
        'requires_model_map': True
    },

                                        ########### 稳温功率智控 ############
    'tiaowen_realtime_powerfix_timeseries_test_setup': {
        'path': 'wenwen_power_control.beta_version.{version}.call.tiaowen_realtime_powerfix_timeseries_test_setup',
        'requires_model_map': True
    },
    'tiaowen_realtime_powerfix_timeseries_test': {
        'path': 'wenwen_power_control.beta_version.{version}.call.tiaowen_realtime_powerfix_timeseries_test',
        'requires_model_map': True
    },
    'tiaowen_realtime_powerfix_timeseries_test_finish': {
        'path': 'wenwen_power_control.beta_version.{version}.call.tiaowen_realtime_powerfix_timeseries_test_finish',
        'requires_model_map': True
    },

                                        ########### 放肩功率智控 ############
    'fangjian_realtime_powerfix_temp_test_setup': {
        'path': 'fangjian_power_control.beta_version.{version}.call.fangjian_realtime_powerfix_temp_test_setup',
        'requires_model_map': True
    },
    'fangjian_realtime_powerfix_temp_test': {
        'path': 'fangjian_power_control.beta_version.{version}.call.fangjian_realtime_powerfix_temp_test',
        'requires_model_map': True
    },
    'fangjian_realtime_powerfix_temp_test_finish': {
        'path': 'fangjian_power_control.beta_version.{version}.call.fangjian_realtime_powerfix_temp_test_finish',
        'requires_model_map': True
    },

                                        ########### 放肩智控 ############
    'fangjian_intelligent_control_test_setup': {
        'path': 'fangjian_speed_rise_control.beta_version.{version}.call.fangjian_intelligent_control_test_setup',
        'requires_model_map': True
    },
    'fangjian_intelligent_control_test': {
        'path': 'fangjian_speed_rise_control.beta_version.{version}.call.fangjian_intelligent_control_test',
        'requires_model_map': True
    },
    'fangjian_intelligent_control_test_finish': {
        'path': 'fangjian_speed_rise_control.beta_version.{version}.call.fangjian_intelligent_control_test_finish',
        'requires_model_map': True
    },

                                        ########### 转肩拉速智控 ############
    'zhuanjian_intelligent_control_test_setup': {
        'path': 'zhuanjian_speed_control.beta_version.{version}.call.zhuanjian_intelligent_control_test_setup',
        'requires_model_map': True
    },
    'zhuanjian_intelligent_control_test': {
        'path': 'zhuanjian_speed_control.beta_version.{version}.call.zhuanjian_intelligent_control_test',
        'requires_model_map': True,
        'haxi_path':'zhuanjian_speed_control.beta_version'
    },
    'zhuanjian_intelligent_control_test_finish': {
        'path': 'zhuanjian_speed_control.beta_version.{version}.call.zhuanjian_intelligent_control_test_finish',
        'requires_model_map': True
    },
                                        ######### 稳温R2计算接口 ########
    'wenwen_calculate_r2': {
        'path': 'wenwen_calculate_r2.call.wenwen_calculate_r2',
        'requires_model_map': False
    },
}

# model_map - 接口的具体映射
model_map = {
    'kongwen': {},
    'tiaowen': {},
    'yinjing': {},
    'fangjian': {},
    'dengjing': {},
    'shouwei': {},
    'tiaowentimeseries': {},
    'dengjingspeedrise': {},
    'fangjianspeedrise': {},
    'fangjiantem': {},
    'zhuanjianspeed': {},
    'shouweilasu': {},
    'intelligentshouwei': {},
    'fangjianintelligent': {},
    'fangjianintelligent_test': {},
}

dict_model_key = {
    "kw_p":[
        "kongwen_realtime_powerfix_setup",
        "kongwen_realtime_powerfix",
        "kongwen_realtime_powerfix_finish",
        "kongwen_realtime_powerfix_test_setup",
        "kongwen_realtime_powerfix_test",
        "kongwen_realtime_powerfix_test_finish",
    ],
    "ww_p": [
        "tiaowen_realtime_powerfix_timeseries_setup",
        "tiaowen_realtime_powerfix_timeseries",
        "tiaowen_realtime_powerfix_timeseries_finish",
        "tiaowen_realtime_powerfix_timeseries_test_setup",
        "tiaowen_realtime_powerfix_timeseries_test",
        "tiaowen_realtime_powerfix_timeseries_test_finish",
    ],
    "fj_p": [
        "fangjian_realtime_powerfix_temp_setup",
        "fangjian_realtime_powerfix_temp",
        "fangjian_realtime_powerfix_temp_finish",
        "fangjian_realtime_powerfix_temp_test_setup",
        "fangjian_realtime_powerfix_temp_test",
        "fangjian_realtime_powerfix_temp_test_finish",
    ],
    "fj_sr": [
        "fangjian_intelligent_control_setup",
        "fangjian_intelligent_control",
        "fangjian_intelligent_control_finish",
        "fangjian_intelligent_control_test_setup",
        "fangjian_intelligent_control_test",
        "fangjian_intelligent_control_test_finish",
    ],
    "zj_s": [
        "zhuanjian_intelligent_control_setup",
        "zhuanjian_intelligent_control",
        "zhuanjian_intelligent_control_finish",
        "zhuanjian_intelligent_control_test_setup",
        "zhuanjian_intelligent_control_test",
        "zhuanjian_intelligent_control_test_finish",

    ],
    "dj_power": [
        "dengjing2_realtime_powerfix_setup",
        "dengjing2_realtime_powerfix",
        "dengjing2_realtime_powerfix_finish",
        "dengjing_realtime_powerfix_test_setup",
        "dengjing_realtime_powerfix_test",
        "dengjing_realtime_powerfix_test_finish"
    ],
    "dj_rise": [
        "dengjing_speed_rise_realtime_powerfix_setup",
        "dengjing_speed_rise_realtime_powerfix",
        "dengjing_speed_rise_realtime_powerfix_finish",
        "dengjing_speed_rise_realtime_powerfix_test_setup",
        "dengjing_speed_rise_realtime_powerfix_test",
        "dengjing_speed_rise_realtime_powerfix_test_finish"
    ],
    "sw_speed":[
        "shouwei_intelligent_control_setup",
        "shouwei_intelligent_control",
        "shouwei_intelligent_control_finish"
        "shouwei_intelligent_control_test_setup",
        "shouwei_intelligent_control_test",
        "shouwei_intelligent_control_test_finish"
    ],
    "sw_power":[
        "shouwei_realtime_powerfix_setup",
        "shouwei_realtime_powerfix",
        "shouwei_realtime_powerfix_finish",
        "shouwei_realtime_powerfix_test_setup",
        "shouwei_realtime_powerfix_test",
        "shouwei_realtime_powerfix_test_finish"
    ]
}
def calculate_file_hash(file_path):
    # 使用 SHA-256 哈希算法
    sha256_hash = hashlib.sha256()
    with open(file_path, "rb") as f:
        # 以二进制模式读取文件并更新哈希对象
        for byte_block in iter(lambda: f.read(4096), b""):
            sha256_hash.update(byte_block)
    return sha256_hash.hexdigest()


def get_max_version_folder(function_name):
    # 检查方法名是否存在于字典中
    if function_name in function_path_map:
        # 获取对应的 path 值
        path = function_path_map[function_name]['path']

        # 按 '.' 分割路径并返回前两部分
        path_parts = path.split('.')
        prefix = '.'.join(path_parts[:2])  # 提取前两部分（即 model 路径部分）


        base_path = prefix.replace('.', '//')  # 如果没有额外路径，则只返回 prefix

        # 检查 base_path 目录下的版本文件夹
        if os.path.exists(base_path) and os.path.isdir(base_path):
            subdirs = [d for d in os.listdir(base_path) if os.path.isdir(os.path.join(base_path, d))]

            # 筛选出符合 vX 版本格式的文件夹
            version_dirs = [d for d in subdirs if re.fullmatch(r'v\d+', d)]

            if version_dirs:
                # 按版本号排序并获取最大版本
                max_version = max(version_dirs, key=lambda v: int(v[1:]))  # 提取数字部分排序
                return max_version  # 只返回最大版本号
        return None  # 如果没有版本文件夹，返回 None
    else:
        return None  # 如果方法名不存在，则返回 None

def find_version(function_name):
    # 检查方法名是否存在于字典中
    if function_name in function_path_map:
        # 获取对应的 path 值
        path = function_path_map[function_name]['path']
        # 按 '.' 分割路径并返回前两部分
        path_parts = path.split('.')
        prefix = '.'.join(path_parts[:2])  # 提取前两部分（即 model 路径部分）
        file_version = os.listdir(prefix.replace('.', '//'))
        return file_version[-1]
    else:
        return None  # 如果方法名不存在，则返回 None
def get_database_from_luno(luno):
    # 提取炉号的字母部分
    letter = luno[0].upper()  # 例如 "A" 或 "B"

    # 获取字母在字母表中的位置，A -> 0, B -> 1, ..., P -> 15
    database_number = (ord(letter) - ord('A')) % 16

    return database_number
# 动态加载并调用函数
def handle_request(func_name, request, conn):
    # 查找接口名对应的路径模板和是否需要传递model_map的标志
    func_info = function_path_map.get(func_name, None)
    if func_info is None:
        return abort(404, description=f"{func_name} NOT FOUND")
    function_path_template = func_info['path']
    requires_model_map = func_info['requires_model_map']
    if func_name in ['yinjingPowerPushOther', 'yinjingPowerPushOther_test', 'yinjing_xunlian', 'tempadjust',
                     'tempadjust_test','predict_jindeng',
                     'yekouju_adjust', 'yekouju_adjust_test', 'edgepoint',
                     'radial_ccd', 'radial_ccd_test', 'bigdata_std_curve', 'dengjing_corr', 'dengjing_tilasu']:
        model_version = 'v1'

        # 使用model_version替换模板中的{version}占位符
        function_path = function_path_template.format(version=model_version)

        # 动态导入模块和函数
        module_name, function_name = function_path.rsplit('.', 1)
        module = importlib.import_module(module_name)
        func = getattr(module, function_name)
        return func(request)  # 不需要model_map
    else:
        device_id = request.json.get('device_id')
        databases = get_database_from_luno(device_id)
        with open("DBUtil/config.json", 'r') as file:
            json_string = file.read()
        config = json.loads(json_string)
        selected_name = config.get("redis", None)
        host = selected_name['host']
        port = selected_name['port']
        password = selected_name['password']
        r = RedisModelManager(redis_host=host, redis_port=port, redis_password=password, redis_db=databases)
        if "setup" in func_name:
            model_version = get_max_version_folder(func_name)
        else:
            condition, model_version = r.check_model_exists(func_name, device_id, dict_model_key)
            # try:
            if not condition:
                model_version = get_max_version_folder(func_name)
        # 使用model_version替换模板中的{version}占位符
        function_path = function_path_template.format(version=model_version)

        # 动态导入模块和函数
        module_name, function_name = function_path.rsplit('.', 1)
        module = importlib.import_module(module_name)
        func = getattr(module, function_name)

        # 如果需要model_map，就传递给目标函数
        if requires_model_map:
            return func(request, conn,r,model_version)
        else:
            return func(request)  # 不需要model_map

        # except Exception as e:
        #     return {'error': str(e)}

# 添加 heart 接口
@app.route('/heart', methods=['POST', 'GET'])
def status():
    return "200"

# 动态路由
@app.route('/<func_name>', methods=['POST', 'GET'])
def dynamic_route(func_name):
    # 调用通用函数进行处理，传递 model_map
    conn = DBUtil.share.get_db()
    return handle_request(func_name, request, conn)





if __name__ == '__main__':
    # server = pywsgi.WSGIServer(('0.0.0.0',80),app)
    # server.serve_forever()
    ENV = os.getenv("FLASK_ENV", "development")  # 读取环境变量，默认为开发环境
    HOST = '0.0.0.0'
    PORT = 80 if ENV == "development" else 5000  # 测试环境用 80 端口，生产环境用 5000 端口

    app.logger.info(f"Starting server in {ENV} mode on {HOST}:{PORT}...")

    server = pywsgi.WSGIServer((HOST, PORT), app)
    server.serve_forever()