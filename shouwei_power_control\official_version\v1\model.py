import yaml
import math
import pickle
import numpy as np


class ShouweiGonglvCorrectionModel():
    @classmethod
    def from_path(cls, keycurves_path, config_path):
        m = cls()
        m.load_config(config_path)
        m.valid = m.load_model(keycurves_path)
        if m.valid:
            m.enable_corr()
            return m

    def load_config(self, config_path):
        with open(config_path) as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.enter_power_step = config['enter_step']['power']
        self.power_adjust_step = config['power_adjust_step']
        self.power_adjust_limit = config['power_adjust_limit']
        self.wait_length = config['wait_length']
        self.diameter_smooth_window = config['diameter_smooth_window']
        self.diameter_adjust_ratio = config['diameter_adjust_ratio']

    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def setup(self, product_type, field_size, device_id, power, dengjing_diameter, dengjing_weight, target_dia, target_power, config, weight_bin_path_11, weight_bin_path_12):
        update_keys = ['power_adjust_step', 'power_adjust_limit', 'wait_length', 'diameter_smooth_window', 'diameter_adjust_ratio']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])
        vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(product_type)))]
        vs = vs.get(device_id, [v for d in vs.values() for v in d])
        vs1 = [v for v in vs if float(v['field_size']) - float(field_size) == 0]
        v = min(vs1, key=lambda v: abs(v['power'][0] - power))
        self.power = (v['power'] + (power - v['power'][0])) if len(target_power) == 0 else np.array(target_power)
        self.diameters = v['diameter'] if len(target_dia) == 0 else np.array(target_dia)
        self.corr, self.before_diameters, self.real_power, self.last_adjust = 0, [], [], 0
        self.prev_r, self.prev_l, self.prev_m = dengjing_diameter / 2, 0, dengjing_weight

        with open(weight_bin_path_11, 'rb') as f:
            self.weight_bin_dict_11 = pickle.load(f)
        with open(weight_bin_path_12, 'rb') as f:
            self.weight_bin_dict_12 = pickle.load(f)
        if product_type == '11':
            self.weight_bin_dict = self.weight_bin_dict_11
        else:
            self.weight_bin_dict = self.weight_bin_dict_12
        self.history_len_weight = {}
        self.offset = [0, 0.1, -0.1, 0.2, -0.2]
        self.history_weight_piancha = {}
        self.count = 0
        self.piancha_flag = None
        self.record_len = 0
        self.corr_flag = None
        self.zhixing_flag = None


    def baseline(self):
        return {'shape_baseline': self.diameters.tolist(), 'power_baseline': self.power.tolist()}

    def finish(self, end_code):
        return self.corr

    def clear_corr(self):
        self.corr, self.last_adjust = 0, None

    def disable_corr(self):
        self.do_corr = False

    def enable_corr(self):
        self.do_corr = True

    def disable_history_corr(self):
        self.do_history_corr = False

    def enable_history_corr(self):
        self.do_history_corr = True

    def predict(self, l, weight):

        if (not self.do_corr) or l >= self.power.size:
            power = self.power[min(int(l), self.power.size - 1)] + (self.corr if self.do_corr else 0)
            return power

        self.history_len_weight[l] = weight

        l = int(l)
        pre_10_len_weight = 0
        if l - self.last_adjust >= self.wait_length:
            for offset in self.offset:
                try:
                    pre_10_len_weight = self.history_len_weight[l - 10 + offset]
                except:
                    print("索引值不存在")
                if pre_10_len_weight != 0:
                    break
            if pre_10_len_weight == 0:
                pre_10_len_weight = weight

            real_weight_piancha = round(weight - pre_10_len_weight, 1)
            standard_weight_piancha = self.weight_bin_dict[l]
            weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

            self.history_weight_piancha[l] = real_weight_piancha
            if weight_piancha > 0.1:
                self.corr += self.power_adjust_step * 2
                self.corr_flag = True
                self.zhixing_flag = None
            elif weight_piancha == 0.1:
                self.corr += self.power_adjust_step
                self.corr_flag = None
                self.zhixing_flag = None
            elif weight_piancha == 0 or weight_piancha < 0:
                self.corr += 0
                self.corr_flag = None
                self.zhixing_flag = None

            if l > 20 and real_weight_piancha - self.history_weight_piancha[l - 10] > 0 and self.piancha_flag == None:
                self.count += 1
                self.piancha_flag = True
                self.record_len = l

            if self.count == 1 and real_weight_piancha - self.history_weight_piancha[l - 20] > 0 and l - self.record_len == 10:
                self.count += 1

            if self.count == 2 and self.corr_flag == None:
                self.corr += self.power_adjust_step
                self.zhixing_flag = True

            if l - self.record_len == 10:
                self.piancha_flag = None
                self.count = 0

            self.last_adjust = l
            self.corr = np.clip(self.corr, -self.power_adjust_limit, self.power_adjust_limit)

        power = self.power[l] + self.corr

        return power