from joblib import load

# 模型需要的特征
#FEATURE = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left','daoliutong_right', 'daoliutong_upleft', 'daoliutong_upright', 'dingguo_finish_yewen', 'dingguo_finish_yekouju']
FEATURE = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left', 'daoliutong_right', 'daoliutong_upleft','daoliutong_upright', 'dingguo_finish_yewen', 'dingguo_finish_guowei','dingguo_finish_yekouju']

class YekoujuAdjustModel:
    def __init__(self, model_path):
        self.model = load(model_path)

    def predict(self, x):
        x_columns = list(map(lambda v: v, FEATURE))
        x = list(map(lambda f: x[f], x_columns))
        return self.model.predict([x])[0]
