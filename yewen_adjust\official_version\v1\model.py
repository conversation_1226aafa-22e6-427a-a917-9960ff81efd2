import heapq
import pickle

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')
class YewenCorrModel:
    def __init__(self, data_file):
        with open(data_file, 'rb') as f:
            self.keycurves = pickle.load(f)

    def predict(self, data):
        #先找单晶
        vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(data['product_type'])))]
        #找热场
        vs = vs[min(vs.keys(), key=lambda v: abs(float(v) - float(data['field_size'])))]
        #找炉台号
        vs1 = vs.get(data['dev_number'], [v for d in vs.values() for v in d])
        #匹配引晶功率
        vs2 = [v for v in vs1 if abs(v['yinjing_power'] - data['yinjing_power']) <= 1]
        #如果找不到引晶功率偏差1以内的，则只匹配单晶、热场不在匹配炉台号
        if len(vs2) == 0:
            vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(data['product_type'])))]
            vs = vs[min(vs.keys(), key=lambda v: abs(float(v) - float(data['field_size'])))]
            vs1 = [v for d in vs.values() for v in d]
            vs2 = [v for v in vs1 if abs(v['yinjing_power'] - data['yinjing_power']) <= 1]
            #如果依然找不到，则不在匹配引晶功率，直接匹配单晶、热场、复投次数
            if len(vs2) == 0:
                vs = self.keycurves[
                    min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(data['product_type'])))]
                vs = vs[min(vs.keys(), key=lambda v: abs(float(v) - float(data['field_size'])))]
                vs1 = vs.get(data['dev_number'], [v for d in vs.values() for v in d])
                v = heapq.nsmallest(3, vs1, key=lambda v: abs(v['feed_number'] - data['feed_number']))
                v_end = min(v, key=lambda v: abs(v['cc_start_tem'] - data['yinjing_temperature_start']))

            else:
                v = heapq.nsmallest(3, vs2, key=lambda v: abs(v['feed_number'] - data['feed_number']))
                v_end = min(v, key=lambda v: abs(v['cc_start_tem'] - data['yinjing_temperature_start']))
        else:
            v = heapq.nsmallest(3, vs2, key=lambda v: abs(v['feed_number'] - data['feed_number']))
            v_end = min(v, key=lambda v: abs(v['cc_start_tem'] - data['yinjing_temperature_start']))
        xishu_after = v_end['cc_end_tem']/v_end['cc_after_fifty_speed']
        A_after = (data['yinjing_average_fifty_lasu'] - v_end['cc_after_fifty_speed']) / (v_end['cc_after_fifty_speed'])
        piancha = A_after * xishu_after
        piancha = np.clip(piancha,-1,1)
        B_after = (v_end['cc_end_tem'] - piancha)
        return B_after
    # def predict_tiantong(self, data):
    #     #先找单晶
    #     vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(data['product_type'])))]
    #     #找热场
    #     vs = vs[min(vs.keys(), key=lambda v: abs(float(v) - float(data['field_size'])))]
    #     #找炉台号
    #     vs1 = vs.get(data['dev_number'], [v for d in vs.values() for v in d])
    #     #匹配引晶功率
    #     vs2 = [v for v in vs1 if abs(v['yinjing_power'] - data['yinjing_power']) <= 1]
    #     #如果找不到引晶功率偏差1以内的，则只匹配单晶、热场不在匹配炉台号
    #     if len(vs2) == 0:
    #         vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(data['product_type'])))]
    #         vs = vs[min(vs.keys(), key=lambda v: abs(float(v) - float(data['field_size'])))]
    #         vs1 = [v for d in vs.values() for v in d]
    #         vs2 = [v for v in vs1 if abs(v['yinjing_power'] - data['yinjing_power']) <= 1]
    #         #如果依然找不到，则不在匹配引晶功率，直接匹配单晶、热场、复投次数
    #         if len(vs2) == 0:
    #             vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(data['product_type'])))]
    #             vs = vs[min(vs.keys(), key=lambda v: abs(float(v) - float(data['field_size'])))]
    #             vs1 = vs.get(data['dev_number'], [v for d in vs.values() for v in d])
    #             v_end = min(vs1, key=lambda v: abs(v['cc_start_tem'] - data['yinjing_temperature_start']))
    #
    #         else:
    #             v_end = min(vs2, key=lambda v: abs(v['cc_start_tem'] - data['yinjing_temperature_start']))
    #     else:
    #         v_end = min(vs2, key=lambda v: abs(v['cc_start_tem'] - data['yinjing_temperature_start']))
    #     xishu_after = v_end['cc_end_tem']/v_end['cc_after_fifty_speed']
    #     A_after = (data['yinjing_average_fifty_lasu'] - v_end['cc_after_fifty_speed']) / (v_end['cc_after_fifty_speed'])
    #     piancha = A_after * xishu_after
    #     piancha = np.clip(piancha,-1,1)
    #     B_after = (v_end['cc_end_tem'] - piancha)
    #     return B_after



