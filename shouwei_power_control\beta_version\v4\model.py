import yaml
import math
import pickle
import numpy as np
from collections import deque


class ShouweiGonglvCorrectionModel():
    @classmethod
    def from_path(cls, keycurves_path, config_path):
        m = cls()
        m.load_config(config_path)
        m.valid = m.load_model(keycurves_path)
        if m.valid:
            m.enable_corr()
            return m

    def load_config(self, config_path):
        with open(config_path) as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.enter_power_step = config['enter_step']['power']
        self.power_adjust_step = config['power_adjust_step']
        self.power_adjust_limit = config['power_adjust_limit']
        self.wait_length = config['wait_length']
        self.diameter_smooth_window = config['diameter_smooth_window']
        self.diameter_adjust_ratio = config['diameter_adjust_ratio']
        self.power_adjust_parameter = config['power_adjust_parameter']

    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def setup(self, product_type, field_size, device_id, power, dengjing_diameter, dengjing_weight, target_dia, target_power, config, weight_bin_path_11, weight_bin_path_12, dengjing_after_100_length,
              dengjing_after_100_grow_lasu, dengjing_after_100_target_lasu, weight_bin_path_11_rate,
              weight_bin_path_105,weight_bin_path_105_rate, weight_remain):
        update_keys = ['power_adjust_step', 'power_adjust_limit', 'wait_length', 'diameter_smooth_window', 'diameter_adjust_ratio', 'power_adjust_parameter']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])
        vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(product_type)))]
        vs = vs.get(device_id, [v for d in vs.values() for v in d])
        vs1 = [v for v in vs if float(v['field_size']) - float(field_size) == 0]
        v = min(vs1, key=lambda v: abs(v['power'][0] - power))

        # 添加目标功率数组处理和前70mm渐进式增速修正（仅对11寸产品启用）
        # 暂时取消此功能
        if False and len(target_power) > 0 and str(product_type) == '11':
            # 目标功率修正配置参数（可根据需要调整）
            power_adjustment_length = 70  # 前70mm长度进行修正
            total_power_increase = 0.7    # 总增加量（分7次均匀增加，每次0.1）

            # 对target_power进行深拷贝，避免修改原始数据
            modified_target_power = np.array(target_power, dtype=float)

            # 实现前70mm的渐进式增速处理
            if len(modified_target_power) > 0:
                # 计算增加步长：每次增加0.1，共7次，在70mm内均匀分布
                increase_per_step = total_power_increase / 7  # 每次增加0.1
                step_interval = power_adjustment_length / 7   # 每10mm进行一次增加

                # 对所有位置应用相应的累积增加量
                for i in range(len(modified_target_power)):
                    if i < power_adjustment_length:
                        # 前70mm：渐进式增速处理
                        # 第0mm不增加，第10mm进行第1次增加，第20mm进行第2次增加，以此类推
                        steps_completed = int((i + 1) / step_interval)
                        steps_completed = min(steps_completed, 7)  # 最多7次增加
                        current_cumulative_increase = steps_completed * increase_per_step
                    else:
                        # 70mm及以后：保持最大增加量0.7
                        current_cumulative_increase = total_power_increase

                    # 应用累积增加量到目标功率
                    modified_target_power[i] = modified_target_power[i] + current_cumulative_increase

                print(f"目标功率修正完成（11寸产品）：前{power_adjustment_length}mm渐进式增速，{power_adjustment_length}mm后保持最大增加量")
                print(f"修正策略：前{power_adjustment_length}mm分7次增加（每次{increase_per_step:.1f}，每{step_interval:.0f}mm），{power_adjustment_length}mm后全部增加{total_power_increase}")
                print(f"修正前后对比（前10个值）：")
                for i in range(min(10, len(target_power))):
                    original_val = target_power[i] if i < len(target_power) else 0
                    modified_val = modified_target_power[i] if i < len(modified_target_power) else 0
                    increase_applied = modified_val - original_val
                    steps_at_position = int((i + 1) / step_interval) if i < power_adjustment_length else 7
                    steps_at_position = min(steps_at_position, 7)
                    print(f"  位置{i}mm: {original_val:.3f} -> {modified_val:.3f} (增加{increase_applied:.3f}, 步数{steps_at_position})")

            self.power = modified_target_power
        elif len(target_power) > 0:
            # 非11寸产品：直接使用提供的target_power，不进行修正
            self.power = np.array(target_power)
            print(f"使用原始目标功率数组（{product_type}寸产品，不进行修正）")
        else:
            # 如果未提供target_power，使用原有逻辑
            self.power = v['power'] + (power - v['power'][0])
            print(f"使用默认功率曲线（{product_type}寸产品）")

        self.diameters = v['diameter'] if len(target_dia) == 0 else np.array(target_dia)
        self.corr, self.before_diameters, self.real_power, self.last_adjust = 0, [], [], 0
        self.prev_r, self.prev_l, self.prev_m = dengjing_diameter / 2, 0, dengjing_weight

        self.weight_remain = weight_remain
        self.product_type = product_type
        with open(weight_bin_path_11, 'rb') as f:
            self.weight_bin_dict_11 = pickle.load(f)
        with open(weight_bin_path_12, 'rb') as f:
            self.weight_bin_dict_12 = pickle.load(f)
        with open(weight_bin_path_105, 'rb') as f:
            self.weight_bin_dict_105 = pickle.load(f)
        if product_type == '11':
            self.weight_bin_dict = self.weight_bin_dict_11
        elif product_type == '10.5':
            self.weight_bin_dict = self.weight_bin_dict_105
        else:
            self.weight_bin_dict = self.weight_bin_dict_12
        self.history_len_weight = {}
        self.offset = [0, 0.1, -0.1, 0.2, -0.2]
        self.history_weight_piancha = {}

        self.pre_last_adjust = 0
        self.previous_power_correction = 0.4
        self.large_xishu = 1.2
        self.small_xishu = 1
        self.corr1 = 0  # 等径后50长拉速修正量
        self.corr1_all = 0
        self.corr2 = 0  # 重量变化率修正量
        self.corr3 = 0  # 大区间重量变化修正量
        self.corr3_all = 0  # 大区间重量变化修正量
        self.corr3_adjust_50 = 0
        self.corr3_adjust_100 = 0
        self.corr3_flag_50 = 0
        self.corr3_flag_100 = 0
        self.integral = 0
        self.slope = 0

        # 对齐长度，计算等径后50长生长拉速-目标拉速的积分值
        min_len = min(len(dengjing_after_100_grow_lasu), len(dengjing_after_100_target_lasu), len(dengjing_after_100_length))
        dengjing_after_100_grow_lasu = dengjing_after_100_grow_lasu[:min_len]
        dengjing_after_100_target_lasu = dengjing_after_100_target_lasu[:min_len]
        dengjing_after_100_length = dengjing_after_100_length[:min_len]

        # 提取后50长
        dengjing_length = dengjing_after_100_length[-1]
        dengjing_after_50 = dengjing_length - 50
        closest_index = min(range(len(dengjing_after_100_length)), key=lambda i: abs(dengjing_after_100_length[i] - dengjing_after_50))
        dengjing_after_50_length = dengjing_after_100_length[closest_index:]
        dengjing_after_50_grow_lasu = dengjing_after_100_grow_lasu[closest_index:]
        dengjing_after_50_target_lasu = dengjing_after_100_target_lasu[closest_index:]

        delta_speed = [g - t for g, t in zip(dengjing_after_50_grow_lasu, dengjing_after_50_target_lasu)]
        # 使用梯形法则进行数值积分
        self.integral = np.trapz(delta_speed, x=dengjing_after_50_length)

        # 计算变化斜率
        dengjing_after_10 = dengjing_length - 10  # 提取后10长数据
        closest_index = min(range(len(dengjing_after_100_length)), key=lambda i: abs(dengjing_after_100_length[i] - dengjing_after_10))
        dengjing_after_10_length = dengjing_after_100_length[closest_index:]
        dengjing_after_10_grow_lasu = dengjing_after_100_grow_lasu[closest_index:]

        # 提取前后三个点，生长拉速，和单位长度
        grow_lasu_start_3_points = dengjing_after_10_grow_lasu[:3]  # 前 3 个点
        grow_lasu_end_3_points = dengjing_after_10_grow_lasu[-3:]  # 后 3 个点
        unit_length_start_3_points = dengjing_after_10_length[:3]  # 前 3 个点
        unit_length_end_3_points = dengjing_after_10_length[-3:]  # 后 3 个点

        avg_growth_speed_start = round(sum(grow_lasu_start_3_points) / len(grow_lasu_start_3_points), 1)
        avg_growth_speed_end = round(sum(grow_lasu_end_3_points) / len(grow_lasu_end_3_points), 1)
        avg_unit_length_start = round(sum(unit_length_start_3_points) / len(unit_length_start_3_points), 1)
        avg_unit_length_end = round(sum(unit_length_end_3_points) / len(unit_length_end_3_points), 1)

        # 生长拉速变化斜率
        self.slope = (avg_growth_speed_end - avg_growth_speed_start) / (avg_unit_length_end - avg_unit_length_start)

        # 判断情况升功率
        if self.integral > 0 and self.slope <= 0:
            self.corr1_all = self.previous_power_correction * self.small_xishu
        if self.integral > 0 and self.slope >= 0:
            self.corr1_all = self.previous_power_correction * self.large_xishu
        if self.integral < 0 and self.slope <= 0:
            self.corr1_all = 0
        if self.integral < 0 and self.slope >= 0:
            self.corr1_all = self.previous_power_correction * self.small_xishu
        if self.integral == 0:
            self.corr1_all = 0

        # 每一长平均修正量
        self.corr1 = self.corr1_all / 10

        self.latest_weights = deque(maxlen=3)
        with open(weight_bin_path_11_rate, 'rb') as f:
            self.weight_bin_dict_11_rate = pickle.load(f)
        with open(weight_bin_path_105_rate, 'rb') as f:
            self.weight_bin_dict_105_rate = pickle.load(f)
        if self.product_type == '11':
            self.large_interval_weight_change = [6.6, 10.8]
        else:
            self.large_interval_weight_change = [5.5, 8.9]
        self.real_time_50_weight_change = 0
        self.real_time_100_weight_change = 0
        self.power_adjust_limit_11 = 2
        self.power_adjust_limit_12 = 2
        if self.product_type == '11':
            self.weight_bin_dict_rate = self.weight_bin_dict_11_rate
        else:
            self.weight_bin_dict_rate = self.weight_bin_dict_105_rate
            
        # 重置剩料重量调整状态
        self.corr_new = 0
        self.corr_new_initialized = False
        self.adjust_points = []
        self.current_adjust_index = 0
        self.corr_new_total = 0.0

        if product_type == '10.5' and field_size == '33':
            self.standard_remnant_weight = 148
            print(f"product_type:{product_type},field_size:{field_size},standard_remnant_weight:{self.standard_remnant_weight}")
        elif product_type == '10.5' and field_size == '36':
            self.standard_remnant_weight = 170
            print(f"product_type:{product_type},field_size:{field_size},standard_remnant_weight:{self.standard_remnant_weight}")
        elif (product_type == '11' or product_type == '12') and field_size == '36':
            self.standard_remnant_weight = 186
            print(f"product_type:{product_type},field_size:{field_size},standard_remnant_weight:{self.standard_remnant_weight}")
        else:
            self.standard_remnant_weight = self.weight_remain  # 或者你可以设置一个默认值
            print(f"没有成功匹配使用本次剩余重量,standard_remnant_weight:{self.standard_remnant_weight}")


    def baseline(self):
        return {'shape_baseline': self.diameters.tolist(), 'power_baseline': self.power.tolist()}

    def finish(self, end_code):
        # 重置新增调整相关的状态
        self.corr_new = 0
        self.corr_new_initialized = False
        self.adjust_points = []
        self.current_adjust_index = 0
        self.corr_new_total = 0.0

        return self.corr

    def clear_corr(self):
        self.corr, self.last_adjust = 0, None

    def disable_corr(self):
        self.do_corr = False

    def enable_corr(self):
        self.do_corr = True

    def disable_history_corr(self):
        self.do_history_corr = False

    def enable_history_corr(self):
        self.do_history_corr = True

    def _calculate_corr_new_based_on_remnant(self, l, weight):
        """
        基于收尾开始时剩料重量的功率调整方法(优化版)
        修改点：只有在差值大于0.01kW时才更新瞬时增量和上次累计值
        """
        # 只在首次调用时进行初始化（在l=0时）
        if not self.corr_new_initialized :
            # self.weight_remain = 145
            print(f"正在初始化剩料重量调整，长度={l}, 重量={self.weight_remain}")
            
            # 计算ΔM并确定调整方向
            delta_M = self.weight_remain - self.standard_remnant_weight
            print(f"标准剩料重量={self.standard_remnant_weight}kg, 实际剩料重量={self.weight_remain}kg, 差值ΔM={delta_M}kg")
            
            # 计算总调整量Δa = k*ΔM
            total_adjust = self.power_adjust_parameter * delta_M

            if self.weight_remain == 0:
                total_adjust = 0
                print("坩埚剩余重量0，传入参数错误")
            # 如果调整量绝对值小于0.01，则忽略调整
            if abs(total_adjust) < 0.01:
                print(f"调整量{total_adjust:.4f}小于0.01，忽略剩料重量调整")
                self.corr_new_initialized = True
                return
            
            # 计算调整步数（以0.01为单位）
            num_steps = abs(int(round(total_adjust * 100)))
            # segment_steps = max(1, num_steps // 2)  # 每段步数（确保至少1步）
            segment_steps = max(1, num_steps)  # 每段步数（确保至少1步）

            print(f"总调整量={total_adjust:.3f}kW, 每段调整步数={segment_steps}")
            
            # 确定调整方向
            if delta_M > 0:  # 剩料多
                segment1_sign = -1  # 0-35mm减少功率
                segment2_sign = 1   # 35-70mm增加功率
                print("调整方向: 前期减缓功率增长，后期加速功率增长")
            else:  # 剩料少
                segment1_sign = 1   # 0-35mm增加功率
                segment2_sign = -1  # 35-70mm减少功率
                print("调整方向: 前期加速功率增长，后期减缓功率增长")
            
            # 创建调整点列表
            self.adjust_points = []
            step_value = 0.01  # 最小调整单位
            
            # 第一段调整点 (0-35mm)
            segment1_positions = np.linspace(0, 35, segment_steps + 1)[:-1]  # 均匀分布但不包括35
            for pos in segment1_positions:
                self.adjust_points.append((pos, segment1_sign * step_value))
            
            # 第二段调整点 (35-70mm)
            segment2_positions = np.linspace(35, 70, segment_steps + 1)[1:]  # 均匀分布但不包括35
            for pos in segment2_positions:
                self.adjust_points.append((pos, segment2_sign * step_value))
            
            # 添加35mm处的平滑过渡点
            if segment_steps > 0:
                # 在35mm处添加一个过渡点，实现平滑过渡
                self.adjust_points.append((35, (segment1_sign + segment2_sign) * step_value / 2))
            
            # 按位置排序调整点
            self.adjust_points.sort(key=lambda x: x[0])
            self.current_adjust_index = 0
            self.corr_new = 0
            self.corr_new_initialized = True
            print(f"剩料重量调整初始化完成, 总调整点数={len(self.adjust_points)}")
            print(f"调整点分布: {self.adjust_points}")
        
        # 确保已初始化
        if not self.corr_new_initialized:
            return 0.0
        
        # 70mm之后不再调整
        if l >= 70:
            # 确保在70mm处调整量归零
            if self.current_adjust_index < len(self.adjust_points):
                # 应用剩余的调整点
                while self.current_adjust_index < len(self.adjust_points):
                    position, adjust_value = self.adjust_points[self.current_adjust_index]
                    
                    # 确保功率只增不减
                    current_power = self.power[min(int(l), len(self.power)-1)] + self.corr
                    new_power = current_power + self.corr_new_total + adjust_value
                    
                    if new_power < current_power:
                        print(f"警告: 在{position}mm处跳过可能导致功率下降的调整 {adjust_value:.3f}")
                    else:
                        self.corr_new_total += adjust_value
                        print(f"在长度={position:.1f}mm处应用调整: {adjust_value:.3f}, 当前总调整={self.corr_new_total:.3f}")
                    
                    self.current_adjust_index += 1
                
                # 验证调整量总和应为零
                total_adjust_applied = sum(adjust for _, adjust in self.adjust_points)
                if abs(total_adjust_applied) > 0.001:
                    print(f"警告: 调整量总和不为零! {total_adjust_applied:.4f}")
                
                # 强制归零
                self.corr_new_total = 0
                print(f"70mm处调整结束, 最终调整量={self.corr_new_total:.3f}")
        
        # 应用当前长度对应的调整点
        while (self.current_adjust_index < len(self.adjust_points) and l >= self.adjust_points[self.current_adjust_index][0]):
            position, adjust_value = self.adjust_points[self.current_adjust_index]
            
            # 确保功率只增不减
            current_power = self.power[min(int(l), len(self.power)-1)] + self.corr
            new_power = current_power + self.corr_new_total + adjust_value
            
            # 如果调整会导致功率下降，则跳过此调整点
            if new_power < current_power:
                print(f"警告: 在{position}mm处跳过可能导致功率下降的调整 {adjust_value:.3f}")
                self.current_adjust_index += 1
                continue
            
            self.corr_new_total += adjust_value
            print(f"在长度={position:.1f}mm处应用调整: {adjust_value:.3f}, 当前总调整={self.corr_new_total:.3f}")
            self.current_adjust_index += 1
        
            self.corr_new += adjust_value
            print(f"在长度={position:.1f}mm处应用调整: {adjust_value:.3f}, 当前总调整={self.corr_new:.3f}")
    
        # # 添加调试信息
        # if l % 10 == 0:  # 每10mm打印一次调整状态
        #     remaining = len(self.adjust_points) - self.current_adjust_index
        #     print(f"长度={l}mm, 瞬时增量={self.corr_new:.3f}kW, 累计总量={self.corr_new_total:.3f}kW, 剩余调整点={remaining}")

    def predict(self, l, weight):
        try:
            # 尝试计算 corr_new
            self._calculate_corr_new_based_on_remnant(l, weight)
            #限制一下调整范围
            if self.corr_new > 1:
                self.corr_new = 1
                print(f"警告: 瞬时增量{self.corr_new:.3f}kW超过上限1kW，限制为1kW")
            elif self.corr_new < -1:
                self.corr_new = -1
                print(f"警告: 瞬时增量{self.corr_new:.3f}kW超过下限-1kW，限制为-1kW")
            elif abs(self.corr_new) < 0.01:  # 理论上不会发生，但保留检查
                self.corr_new = 0.01 if self.corr_new > 0 else -0.01
                print(f"警告: 瞬时增量{self.corr_new:.3f}kW小于0.01kW，强制为{self.corr_new:.3f}kW")
            else:
                self.corr_new = self.corr_new
            # 检查 corr_new 是否是浮点数，如果不是则设为 0.0
            if not isinstance(self.corr_new, float):
                self.corr_new = 0.0
                
        except Exception as e:  # 如果计算过程出错
            print(f"predict 方法出错: {e}")  # 可选：打印错误信息（调试用）
            self.corr_new = 0.0  # 确保 corr_new 是浮点数 0.0
        
        if (not self.do_corr) or l >= self.power.size:
            power = self.power[min(int(l), self.power.size - 1)] + (self.corr if self.do_corr else 0) + self.corr_new
            return power

        self.history_len_weight[l] = weight
        # 用于存储当前最新的三个重量值
        self.latest_weights.append(weight)
        if self.product_type == '11' or self.product_type == '10.5':
            l = int(l)
            # 在前10长修正功率
            if l <= self.wait_length:
                if l - self.pre_last_adjust == 1:
                    self.corr += self.corr1
                    self.pre_last_adjust = l

            if l - self.last_adjust >= self.wait_length:

                matching_weights = [0, 0, 0]
                data_list = [(key, value) for key, value in self.history_len_weight.items()]
                for increment in self.offset:
                    adjusted_length = l - 10 + increment
                    # 遍历列表查找匹配的单位长度
                    for i in range(2, len(data_list)):  # 从索引2开始，确保有前两个索引
                        if data_list[i][0] == adjusted_length:
                            # 找到匹配的单位长度，提取前两个索引的晶体重量值和当前匹配的晶体重量值
                            matching_weights = [data_list[i - 2][1], data_list[i - 1][1], data_list[i][1]]
                            break
                    if matching_weights:  # 如果找到了匹配的值，跳出增量调整循环
                        break
                else:
                    print("没有匹配到目标单位长度。")

                # 剔除异常值，相邻之间的两个数差值大于0.2
                def remove_outliers(lst, threshold=0.2):
                    # 保留第一个元素
                    filtered_lst = [lst[0]]
                    # 从第二个元素开始检查
                    for i in range(1, len(lst)):
                        # 如果当前元素与前一个元素的差值大于阈值，则跳过当前元素
                        if abs(lst[i] - lst[i - 1]) > threshold:
                            continue
                        # 否则，保留当前元素
                        filtered_lst.append(lst[i])
                    return filtered_lst

                matching_weights = remove_outliers(matching_weights)

                pre_10_len_weight = round(sum(matching_weights) / len(matching_weights), 2)

                if pre_10_len_weight == 0:  # 预防找不到前10长重量的情况
                    pre_10_len_weight = weight

                # 计算当前重量的均值
                latest_weights = list(self.latest_weights)
                latest_weights = remove_outliers(latest_weights)
                real_weight = round(sum(latest_weights) / len(latest_weights), 2)

                real_weight_piancha = round(real_weight - pre_10_len_weight, 1)
                standard_weight_piancha = self.weight_bin_dict[l]
                weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

                self.history_weight_piancha[l] = real_weight_piancha
                if weight_piancha > 0.1:
                    self.corr += self.power_adjust_step * 2
                elif weight_piancha == 0.1:
                    self.corr += self.power_adjust_step
                elif weight_piancha == 0 or weight_piancha < 0:
                    self.corr += 0

                # 根据大区间重量变化率去修正功率
                if l == 50:
                    standard_large_interval_weight_change = self.large_interval_weight_change[0]
                    for i in [50, 40, 30, 20, 10]:
                        self.real_time_50_weight_change += self.history_weight_piancha[i]
                    if round(self.real_time_50_weight_change - standard_large_interval_weight_change, 1) > 0.2:
                        self.corr3_all = self.power_adjust_step * 2
                        self.corr3 = self.corr3_all / 5
                        self.corr += self.corr3
                        self.corr3_adjust_50 = l
                        self.corr3_flag_50 = 1

                # 在后50长均匀修正
                if l - self.corr3_adjust_50 <= 40 and self.corr3_flag_50 == 1:
                    self.corr += self.corr3

                if l == 100:
                    standard_large_interval_weight_change = self.large_interval_weight_change[1]
                    for i in [100, 90, 80, 70, 60]:
                        self.real_time_100_weight_change += self.history_weight_piancha[i]
                    self.real_time_100_weight_change += self.real_time_50_weight_change
                    if round(self.real_time_100_weight_change - standard_large_interval_weight_change, 1) > 0.2:
                        self.corr3_all = self.power_adjust_step * 2
                        self.corr3 = self.corr3_all / 5
                        self.corr += self.corr3
                        self.corr3_adjust_100 = l
                        self.corr3_flag_100 = 1

                # 在后50长均匀修正
                if l - self.corr3_adjust_100 <= 40 and self.corr3_flag_100 == 1:
                    self.corr += self.corr3

                # 在大于150长后，将标志位置0
                if l >= 150:
                    self.corr3_flag_50 = 0
                    self.corr3_flag_100 = 0
                    self.corr3 = 0

                # 根据重量变化率去修正功率
                if l >= 20 and self.corr3_flag_50 == 0 and self.corr3_flag_100 == 0:
                    standard_weight_rate = self.weight_bin_dict_rate[l]
                    try:
                        real_time_rate = self.history_weight_piancha[l] / self.history_weight_piancha[l - 10]
                        if real_time_rate > standard_weight_rate:
                            self.corr2 = 0.1
                            self.corr += self.corr2
                    except:
                        print("存在错误")

                self.last_adjust = l
                self.corr = np.clip(self.corr, -self.power_adjust_limit_11, self.power_adjust_limit_11)

        if self.product_type == '12':
            l = int(l)
            if l <= self.wait_length:
                if l - self.pre_last_adjust == 1:
                    self.corr += self.corr1
                    self.pre_last_adjust = l
            if l - self.last_adjust >= self.wait_length:

                matching_weights = [0, 0, 0]
                data_list = [(key, value) for key, value in self.history_len_weight.items()]
                for increment in self.offset:
                    adjusted_length = l - 10 + increment
                    # 遍历列表查找匹配的单位长度
                    for i in range(2, len(data_list)):  # 从索引2开始，确保有前两个索引
                        if data_list[i][0] == adjusted_length:
                            # 找到匹配的单位长度，提取前两个索引的晶体重量值和当前匹配的晶体重量值
                            matching_weights = [data_list[i - 2][1], data_list[i - 1][1], data_list[i][1]]
                            break
                    if matching_weights:  # 如果找到了匹配的值，跳出增量调整循环
                        break
                else:
                    print("没有匹配到目标单位长度。")

                # 剔除异常值，相邻之间的两个数差值大于0.2
                def remove_outliers(lst, threshold=0.2):
                    # 保留第一个元素
                    filtered_lst = [lst[0]]
                    # 从第二个元素开始检查
                    for i in range(1, len(lst)):
                        # 如果当前元素与前一个元素的差值大于阈值，则跳过当前元素
                        if abs(lst[i] - lst[i - 1]) > threshold:
                            continue
                        # 否则，保留当前元素
                        filtered_lst.append(lst[i])
                    return filtered_lst

                matching_weights = remove_outliers(matching_weights)

                pre_10_len_weight = round(sum(matching_weights) / len(matching_weights), 2)

                if pre_10_len_weight == 0:  # 预防找不到前15长重量的情况
                    pre_10_len_weight = weight

                # 计算当前重量的均值
                latest_weights = list(self.latest_weights)
                latest_weights = remove_outliers(latest_weights)
                real_weight = round(sum(latest_weights) / len(latest_weights), 2)

                real_weight_piancha = round(real_weight - pre_10_len_weight, 1)
                standard_weight_piancha = self.weight_bin_dict[l]
                weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

                self.history_weight_piancha[l] = real_weight_piancha
                if weight_piancha > 0.1:
                    self.corr += self.power_adjust_step * 3
                elif weight_piancha == 0.1:
                    self.corr += self.power_adjust_step * 2
                elif weight_piancha == 0 or weight_piancha < 0:
                    self.corr += 0

                self.last_adjust = l
                self.corr = np.clip(self.corr, -self.power_adjust_limit_12, self.power_adjust_limit_12)

        # 整合新的剩料重量调整量
        power = self.power[l] + self.corr + self.corr_new
        # power = self.power[l] + self.corr_new

        print(f"l: {l}, power: {power}，corr: {self.corr}，corr_new: {self.corr_new}")
        return power