import yaml
import math
import pickle
import numpy as np
from datetime import datetime
import pickle
from collections import deque
class ShouweiIntelligentCorrectionModel():
    @classmethod
    def from_path(cls, keycurves_path, config_path):
        m = cls()
        m.load_config(config_path)
        m.valid = m.load_model(keycurves_path)
        if m.valid:
            m.enable_corr()
            return m

    def load_config(self, config_path):
        with open(config_path) as f:
            config = yaml.load(f, Loader=yaml.FullLoader)['gonglv']
        self.enter_power_step = config['enter_step']['power']
        self.power_adjust_step = config['power_adjust_step']
        self.power_adjust_limit = config['power_adjust_limit']
        # self.wait_length = config['wait_length']
        self.diameter_smooth_window = config['diameter_smooth_window']
        self.diameter_adjust_ratio = config['diameter_adjust_ratio']
        self.lasu_adjust_step_weight = config['lasu_adjust_step_weight']
        self.lasu_adjust_Increase = config['lasu_adjust_increase']

    def load_model(self, keycurves_path):
        with open(keycurves_path, 'rb') as f:
            self.keycurves = pickle.load(f)
        return len(self.keycurves) != 0

    def match_data(self, data_dict, device_no, product_size, hot_zone_size, initial_pull_speed, end_time):
        # 1. 初步过滤出符合设备号、产品尺寸和热场尺寸的项
        matching_items = {k: v for k, v in data_dict.items() if k[0] == device_no and k[1] == product_size and k[2] == hot_zone_size}

        # 如果没有找到符合设备号的匹配项，则根据产品尺寸和热场尺寸过滤
        if not matching_items:
            matching_items = {k: v for k, v in data_dict.items() if k[1] == product_size and k[2] == hot_zone_size}

        # 如果依然没有找到匹配项，根据产品尺寸过滤
        if not matching_items:
            matching_items = {k: v for k, v in data_dict.items() if k[1] == product_size}

        # 2. 从匹配项中选择进收尾时初始拉速在指定范围内的曲线
        speed_filtered_items = {k: v for k, v in matching_items.items() if (initial_pull_speed - 10) <= float(k[3]) <= (initial_pull_speed + 10)}

        # 如果没有找到符合初始拉速范围的项，返回前面匹配项
        if not speed_filtered_items:
            speed_filtered_items = matching_items

        # 3. 在符合初始拉速范围的项中，选择最近一段时间内的曲线（例如近1个月的）
        from datetime import datetime, timedelta
        current_time = datetime.strptime(end_time, "%Y/%m/%d %H:%M:%S")
        one_month_ago = current_time - timedelta(days=60)

        recent_matches = {k: v for k, v in speed_filtered_items.items() if datetime.strptime(k[4], "%Y/%m/%d %H:%M:%S") >= one_month_ago}

        # 如果存在近1个月内的匹配项，使用这些项；否则，使用所有符合初始拉速范围的项
        final_candidates = recent_matches if recent_matches else speed_filtered_items

        # 4. 在最终候选项中，选择晶体增长重量最小的作为最终结果
        final_match = min(final_candidates.items(), key=lambda item: item[0][-1])  # item[0][-1] 是晶体增长重量

        # 返回最终匹配的记录
        return final_match[1]

    def filter_by_furnace_size_model(self, pickle_file, target_furnace_id, target_product_size, target_thermal_model):
        with open(pickle_file, 'rb') as f:
            data_dict = pickle.load(f)
        key_with_furnace = (target_furnace_id, target_product_size, target_thermal_model)
        if key_with_furnace in data_dict:
            return data_dict[key_with_furnace]
        for (furnace_id, product_size, thermal_model), value in data_dict.items():
            if product_size == target_product_size and thermal_model == target_thermal_model:
                print("找到的曲线：", furnace_id)
                return value
        return None

    def get_crystal_lift_by_unit_length(self, filtered_data, target_unit_length):
        for unit_length, crystal_lift in filtered_data:
            if unit_length == target_unit_length:
                return crystal_lift
        return None

    def filter_by_product_size_model(self, pickle_file, target_product_size):
        with open(pickle_file, 'rb') as f:
            data_dict = pickle.load(f)
        key_with_furnace = target_product_size
        if key_with_furnace in data_dict:
            return data_dict[key_with_furnace]
        return None

    def get_correction_factor_by_unit_length(self, filtered_data, target_unit_length):
        for unit_length, factor in filtered_data:
            if unit_length == target_unit_length:
                return factor
        return None

    def setup(self, product_type, field_size, device_id, power, dengjing_diameter, dengjing_weight, target_dia, target_power, config, lasu_keycurves_path, init_lasu, weight_bin_path_11, weight_bin_path_12, weight_bin_path_105, target_speed=[]):
        update_keys = ['power_adjust_step', 'power_adjust_limit', 'wait_length', 'diameter_smooth_window', 'diameter_adjust_ratio', 'lasu_adjust_step_weight', 'lasu_adjust_increase']
        for key in update_keys:
            if key in config and config[key] is not None:
                setattr(self, key, config[key])
        vs = self.keycurves[min(self.keycurves.keys(), key=lambda v: abs(float(v) - float(product_type)))]
        vs = vs.get(device_id, [v for d in vs.values() for v in d])
        vs1 = [v for v in vs if float(v['field_size']) - float(field_size) == 0]
        v = min(vs1, key=lambda v: abs(v['power'][0] - power))
        self.power = (v['power'] + (power - v['power'][0])) if len(target_power) == 0 else np.array(target_power)
        self.diameters = v['diameter'] if len(target_dia) == 0 else np.array(target_dia)

        # 添加目标拉速数组处理
        # 如果提供了target_speed，使用用户指定的目标拉速；否则设置为空数组（将使用原有方法）
        self.target_speed = np.array(target_speed) if len(target_speed) > 0 else np.array([])

        self.corr, self.before_diameters, self.real_power, self.last_adjust = 0, [], [], 14
        self.prev_r, self.prev_l, self.prev_m = dengjing_diameter / 2, 0, dengjing_weight

        self.reallasu = 0
        self.last_reallasu = 0  # 添加变量保存上一次的拉速值
        self.weight_change = 0
        self.lasu_keycurves_path, self.device_id , self.product_type, self.field_size = lasu_keycurves_path, device_id, product_type, field_size
        self.init_lasu = init_lasu
        self.origin_lasu = 0
        self.origin_lasus = []
        self.origin_lasus.append((0.0,self.init_lasu))

        # 保留历史数据匹配逻辑作为target_speed的回退机制，确保系统稳定性
        self.time = datetime.now().strftime('%Y/%m/%d %H:%M:%S')
        with open(self.lasu_keycurves_path, 'rb') as f:
            self.data_dict = pickle.load(f)
        self.lasu_keys = self.match_data(self.data_dict, self.device_id, self.product_type, self.field_size, self.init_lasu, self.time)
        self.lasu_keys = [(round(float(x[0]), 1), round(float(x[1]), 1)) for x in self.lasu_keys]
        self.gengxin_list = []

        self.weight_changes = []
        self.init_weight_change = 0
        self.baseline_error = 0
        self.baseline_flag = None
        # 添加标志位记录当前使用的拉速获取方法
        self.using_target_speed_array = False
        self.history_jilu = []

        # 新版拉速
        self.history_len_weight = {}
        self.offset = [0, 0.1, -0.1, 0.2, -0.2]
        with open(weight_bin_path_11, 'rb') as f:
            self.weight_bin_dict_11 = pickle.load(f)
        with open(weight_bin_path_12, 'rb') as f:
            self.weight_bin_dict_12 = pickle.load(f)
        with open(weight_bin_path_105, 'rb') as f:
            self.weight_bin_dict_105 = pickle.load(f)
        if product_type == '11':
            self.weight_bin_dict = self.weight_bin_dict_11
        elif product_type == '10.5':
            self.weight_bin_dict = self.weight_bin_dict_105
        else:
            self.weight_bin_dict = self.weight_bin_dict_12
        self.lasu_correction = 0
        self.lasu_adjust_step = 0.2
        self.wait_length = 1

        self.latest_weights = deque(maxlen=3)

        self.real_lasu = []
        self.uper_150_flag = False
        self.chang_150 = False
        self.flag_150 = False
        self.baseline_error_150 = None
        # 初始化0-150mm重量增量修正的起始重量
        self.last_correction_weight_0_150 = None
        # 初始化记录150mm时重量的变量
        self.weight_at_150mm = None

    def baseline(self):
        return {'shape_baseline': self.diameters.tolist(), 'power_baseline': self.power.tolist()}

    def finish(self):
        pass

    def clear_corr(self):
        self.corr, self.last_adjust = 0, None

    def disable_corr(self):
        self.do_corr = False

    def enable_corr(self):
        self.do_corr = True

    def disable_history_corr(self):
        self.do_history_corr = False

    def enable_history_corr(self):
        self.do_history_corr = True

    def predict(self, l, weight):
        length = round(float(l), 1)
        self.history_len_weight[l] = weight
        self.gengxin_list.append((l, weight))  # 用于更新曲线

        # 用于存储当前最新的三个重量值
        self.latest_weights.append(weight)

        l = int(l)
        if l - self.last_adjust >= self.wait_length:
            # 寻找前15长的三个重量
            matching_weights = [0, 0, 0]
            data_list = [(key, value) for key, value in self.history_len_weight.items()]
            for increment in self.offset:
                adjusted_length = l - 15 + increment
                # 遍历列表查找匹配的单位长度
                for i in range(2, len(data_list)):  # 从索引2开始，确保有前两个索引
                    if data_list[i][0] == adjusted_length:
                        # 找到匹配的单位长度，提取前两个索引的晶体重量值和当前匹配的晶体重量值
                        matching_weights = [data_list[i - 2][1], data_list[i - 1][1], data_list[i][1]]
                        break
                if matching_weights:  # 如果找到了匹配的值，跳出增量调整循环
                    break
            else:
                print("没有匹配到目标单位长度。")

            # 剔除异常值，相邻之间的两个数差值大于0.2
            def remove_outliers(lst, threshold=0.2):
                # 保留第一个元素
                filtered_lst = [lst[0]]
                # 从第二个元素开始检查
                for i in range(1, len(lst)):
                    # 如果当前元素与前一个元素的差值大于阈值，则跳过当前元素
                    if abs(lst[i] - lst[i - 1]) > threshold:
                        continue
                    # 否则，保留当前元素
                    filtered_lst.append(lst[i])
                return filtered_lst

            matching_weights = remove_outliers(matching_weights)

            # 计算前15长的均值
            pre_15_len_weight = round(sum(matching_weights) / len(matching_weights), 2)

            if pre_15_len_weight == 0:  # 预防找不到前15长重量的情况
                pre_15_len_weight = weight

            # 计算当前重量的均值
            latest_weights = list(self.latest_weights)
            latest_weights = remove_outliers(latest_weights)
            real_weight = round(sum(latest_weights) / len(latest_weights), 2)

            # 计算前15长重量改变量
            real_weight_piancha = round(real_weight - pre_15_len_weight, 1)
            standard_weight_piancha = self.weight_bin_dict[l]
            weight_piancha = round(real_weight_piancha - standard_weight_piancha, 1)

            def limit_range(value):
                return max(-1, min(1, value))

            if self.product_type == "11" or self.product_type == "10.5":
                self.lasu_correction = limit_range((weight_piancha / 0.1) * 0.2)  # 限制在[-1,1]之间

            elif self.product_type == "12":
                if weight_piancha > 0.1:
                    self.lasu_correction = limit_range((weight_piancha / 0.1) * 0.2)  # 限制在[-1,1]之间
                elif weight_piancha == 0.1:
                    self.lasu_correction = limit_range((weight_piancha / 0.1) * 0.1)  # 限制在[-1,1]之间
                else:
                    self.lasu_correction = 0
            else:
                pass
            self.last_adjust = l
            if l > 150 and weight_piancha <= 0:
                self.uper_150_flag = True
            else:
                self.uper_150_flag = False

            # 保存原方法计算的修正值作为备用
            original_correction = self.lasu_correction
            
            # 调用新的方法计算拉速修正，出错时回退使用原方法结果
            try:
                self.lasu_correction = self.calculate_lasu_correction(length, weight)
                # print(f"当前修正值: {self.lasu_correction}")
            except Exception as e:
                # 出错时恢复使用原方法计算的值
                self.lasu_correction = original_correction
                print(f"新方法计算出错，使用原方法值: {self.lasu_correction}，错误信息: {str(e)}")

        # 修改：根据产品尺寸选择基础拉速获取方法，并记录使用的方法
        self.using_target_speed_array = False  # 重置标志位
        try:
            # 仅对11寸产品使用目标拉速数组方法
            if self.product_type == '11' and len(self.target_speed) > 0:
                # 将长度转换为数组索引，与功率控制模块保持一致的处理方式
                # 数组索引直接对应整数长度值，索引0=0mm, 索引1=1mm, 索引10=10mm
                length_index = int(length)  # 直接转换为整数索引，与功率控制模块一致
                if 0 <= length_index < len(self.target_speed):
                    self.origin_lasu = float(self.target_speed[length_index])
                    self.using_target_speed_array = True  # 标记成功使用target_speed数组
                    print(f"使用target_speed数组获取基础拉速: 长度={length}, 索引={length_index}, 拉速={self.origin_lasu}")
                else:
                    # 如果超出数组范围，安全检查后使用最后一个值或回退到历史方法
                    if len(self.target_speed) > 0:
                        self.origin_lasu = float(self.target_speed[-1])
                        self.using_target_speed_array = True  # 标记成功使用target_speed数组
                        print(f"使用target_speed数组最后值: 长度={length}, 拉速={self.origin_lasu}")
                    else:
                        # 数组为空时，抛出异常回退到历史方法
                        raise Exception(f"target_speed数组为空，无法获取拉速值")
            else:
                # 对于非11寸产品或target_speed为空，直接使用原有的历史数据查找方法
                raise Exception(f"产品尺寸 {self.product_type} 不使用target_speed数组或target_speed为空，跳转到原有方法")
        except Exception as e:
            print(f"无法从目标拉速数组获取基础拉速，产品尺寸: {self.product_type}, 长度: {length}, 错误: {e}")
            print("回退到原有的历史数据查找方法")
            # 异常情况下回退到原有的方法
            self.using_target_speed_array = False  # 标记使用历史曲线方法
            try:
                self.origin_lasu = self.get_crystal_lift_by_unit_length(self.lasu_keys, length)
                print(f"使用历史曲线获取基础拉速: 长度={length}, 拉速={self.origin_lasu}")
            except Exception as e2:
                print(f"原有方法也失败，长度: {length}, 错误: {e2}")
                # 最后的回退：使用初始拉速
                self.origin_lasu = self.init_lasu

        if l <= 150:
            if self.origin_lasu == None:
                self.origin_lasu = self.origin_lasus[-1][1]
                self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                self.real_lasu.append((l, self.reallasu))
            else:
                # 根据使用的拉速获取方法决定是否计算baseline_error
                if self.baseline_flag == None:
                    if self.using_target_speed_array:
                        # 使用target_speed数组时，不进行baseline_error补偿
                        # 因为target_speed已经是为当前条件设计的目标值
                        self.baseline_error = 0.0
                        print(f"使用target_speed数组，baseline_error设为0，避免不必要的偏差补偿")
                    else:
                        # 使用历史曲线时，需要进行baseline_error补偿以适配当前初始条件
                        self.baseline_error = round(float(self.init_lasu - self.origin_lasu), 1)
                        print(f"使用历史曲线，计算baseline_error={self.baseline_error} (init_lasu={self.init_lasu} - origin_lasu={self.origin_lasu})")
                    self.baseline_flag = True
                self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                self.origin_lasus.append((l, self.origin_lasu))
                self.real_lasu.append((l, self.reallasu))
        else:
            if self.uper_150_flag:
                self.chang_150 = True
                self.baseline_error_150 = None
                self.flag_150 = True
                self.reallasu = self.real_lasu[-1][1]
                self.origin_lasus.append((l,self.origin_lasu))
                self.real_lasu.append((l, self.reallasu))
            elif not self.chang_150:
                if self.origin_lasu == None:
                    self.origin_lasu = self.origin_lasus[-1][1]
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                    self.real_lasu.append((l, self.reallasu))
                else:
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error
                    self.origin_lasus.append((l,self.origin_lasu))
                    self.real_lasu.append((l, self.reallasu))
            else:
                if self.flag_150 and self.baseline_error_150 is None:
                    self.flag_150 = False
                    if self.origin_lasu == None:
                        self.origin_lasu = self.origin_lasus[-1][1]
                    # 150mm后的baseline_error_150计算也需要考虑使用的方法
                    if self.using_target_speed_array:
                        # 使用target_speed数组时，150mm后也不进行额外的偏差补偿
                        # 直接使用实际拉速与目标拉速的差值，但考虑到可能的动态调整需求，保持一定的适应性
                        self.baseline_error_150 = 0.0
                        print(f"150mm后使用target_speed数组，baseline_error_150设为0")
                    else:
                        # 使用历史曲线时，计算150mm后的偏差补偿
                        self.baseline_error_150 = round(float(self.real_lasu[-1][1] - self.origin_lasu), 1)
                        print(f"150mm后使用历史曲线，计算baseline_error_150={self.baseline_error_150}")
                if self.origin_lasu == None:
                    self.origin_lasu = self.origin_lasus[-1][1]
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error_150
                    self.real_lasu.append((l, self.reallasu))
                else:
                    self.reallasu = self.origin_lasu + self.lasu_correction + self.baseline_error_150
                    self.origin_lasus.append((l,self.origin_lasu))
                    self.real_lasu.append((l, self.reallasu))
        try:
            # 在长度大于21时，确保新的拉速值不小于上一次的拉速值
            if l > 21:
                if self.reallasu < self.last_reallasu:
                    self.reallasu = self.last_reallasu
                self.last_reallasu = self.reallasu  # 更新上一次的拉速值
            else:
                self.last_reallasu = self.reallasu  # 更新上一次的拉速值
        except Exception as e:
            print(f"更新拉速值错误: {str(e)}")

        # 历史曲线自增加功能
        if length == 170.0 and (self.product_type == '11' or self.product_type == '10.5')and len(self.data_dict) <= 500:
            all_weight = round(weight - self.prev_m, 1)
            key = (
                self.device_id,
                self.product_type,
                self.field_size,
                self.init_lasu,
                self.time,
                all_weight
            )
            self.data_dict[key] = self.gengxin_list
            output_pickle_path = 'shouwei_lasu_control/beta_version/v1/model_data/shouwei_lasu_correction.bin'  # 项目路径
            with open(output_pickle_path, 'wb') as pickle_file:
                pickle.dump(self.data_dict, pickle_file)

        if length == 210.0 and self.product_type == '12' and len(self.data_dict) <= 500:
            all_weight = round(weight - self.prev_m, 1)
            key = (
                self.device_id,
                self.product_type,
                self.field_size,
                self.init_lasu,
                self.time,
                all_weight
            )
            self.data_dict[key] = self.gengxin_list
            output_pickle_path = 'shouwei_lasu_control/beta_version/v1/model_data/shouwei_lasu_correction.bin'  # 项目路径
            with open(output_pickle_path, 'wb') as pickle_file:
                pickle.dump(self.data_dict, pickle_file)

        # 功率
        if (not self.do_corr) or l >= self.power.size:
            power = self.power[min(int(l), self.power.size - 1)] + (self.corr if self.do_corr else 0)
            return power, self.reallasu

        def compute_diameter(R, dm, dh):
            if R <= 0 or abs(dh) <= 1e-6 or abs(dm) <= 1e-6:
                return None
            p = 2.34 * 1e-6  # kg/mm^3
            try:
                r = (math.sqrt(3 * dm / (p * math.pi * dh) - 0.75 * R * R) - 0.5 * R)
                return r
            except:
                return None

        compute_r = compute_diameter(self.prev_r, weight - self.prev_m, l - self.prev_l)
        if compute_r == None:
            diameter = None
        else:
            diameter = compute_r * 2
        l = int(l)
        if diameter is not None and (self.last_adjust is None or l - self.last_adjust > self.wait_length):
            # 原有的功率调整逻辑保持不变
            # The original power adjustment logic remains unchanged.
            if abs(self.diameters[l] - diameter) / self.diameters[l] > self.diameter_adjust_ratio:
                self.corr += (1 if diameter > self.diameters[l] else -1) * self.power_adjust_step
                self.corr = np.clip(self.corr, -self.power_adjust_limit, self.power_adjust_limit)
                # last_adjust 的更新可能需要根据新的触发条件进行调整，但根据您的要求，暂时保持原样。
                # The update of last_adjust might need to be adjusted based on the new triggers,
                # but for now, it is kept as is based on your instruction to not change original logic.
                # self.last_adjust = l # This line is moved below the calculate_lasu_correction call to keep it with the original block.

        power = self.power[l] + self.corr

        return power, self.reallasu


    def get_standard_length_by_weight(self, current_weight):
        """
        根据当前重量查找对应的标准长度（优先返回差值最小的长度，若差值相同则返回较小的长度）
        Args:
            current_weight (float): 当前重量（单位：kg）
        Returns:
            float: 最接近的标准长度，若未找到则返回当前重量对应的默认长度
        """
        # print(f"\nDEBUG: 开始查找标准长度，当前重量 = {current_weight}")
        
        # 生成标准重量字典
        standard_weight_dict = self.generate_weight_dict()
        # print(f"DEBUG: 标准重量字典内容 = {standard_weight_dict}")
        
        closest_length = None
        min_weight_diff = float('inf')
        
        # 按标准长度升序排列，确保在相同差值时优先选择较小的长度
        sorted_lengths = sorted(standard_weight_dict.keys())
        # print(f"DEBUG: 排序后的长度列表 = {sorted_lengths}")
        
        for standard_length in sorted_lengths:
            # 获取该长度对应的标准重量
            standard_weight = standard_weight_dict[standard_length]
            weight_diff = abs(current_weight - standard_weight)
            # print(f"DEBUG: 比较长度 {standard_length}，标准重量 {standard_weight}，差值 {weight_diff}")
            
            # 判断条件：差值更小，或差值相等但当前长度更小
            if (weight_diff < min_weight_diff) or \
            (weight_diff == min_weight_diff and standard_length < closest_length):
                min_weight_diff = weight_diff
                closest_length = standard_length
                # print(f"DEBUG: 更新最接近长度 = {closest_length}，最小差值 = {min_weight_diff}")
        
        # 若未找到匹配值，默认返回当前长度
        result = closest_length if closest_length is not None else current_weight
        # print(f"DEBUG: 最终返回长度 = {result}")
        return result
    def calculate_lasu_correction(self, current_length, current_weight):
        """根据当前长度和重量计算拉速修正值（不满足条件时修正值为0）"""
        def limit_range(value):
            return max(0, min(1.0, value))

        # 初始化修正值为0
        new_correction = 0.0

        # ------------------------- 0-150mm 逻辑 -------------------------
        if current_length <= 150:
            # 初始化起始重量记录
            if self.last_correction_weight_0_150 is None:
                self.last_correction_weight_0_150 = current_weight
                # print("首次记录时，修正值为0")
                return 0.0  # 首次记录时不触发修正

            # 计算重量增量
            weight_increment = current_weight - self.last_correction_weight_0_150
            if weight_increment >= self.lasu_adjust_step_weight:  # 使用配置的重量调整步长
                # 获取标准长度（若未找到返回0）
                standard_length = self.get_standard_length_by_weight(current_weight)
                if standard_length is not None and current_length < standard_length:
                    new_correction = limit_range(self.lasu_adjust_Increase)  # 使用配置的拉速增加步长
                    # print(f"当前修正值: {new_correction}，位置01")
                # 更新基准重量
                self.last_correction_weight_0_150 = current_weight
                # print(f"当前长度：{current_length}，当前重量：{current_weight}，标准长度：{standard_length}，当前修正值: {new_correction}，位置01-1")
            else:
                # 不满足重量增量，修正值保持为0
                new_correction = 0.0
                # print(f"当前重量：{current_weight}，上次重量：{self.last_correction_weight_0_150}，当前修正值: {new_correction}，位置02")

        # ------------------------- 150-170mm 逻辑 -------------------------
        elif 150 < current_length <= 170:
            # 获取150mm时的实际重量
            if self.weight_at_150mm is None:
                # 首先尝试获取150mm的重量
                self.weight_at_150mm = self.history_len_weight.get(150.0)
                # print(f"位置02-1")
                
                # 如果没有150mm的重量，尝试获取145-150mm之间的重量
                if self.weight_at_150mm is None and self.history_len_weight:
                    valid_lengths = [l for l in self.history_len_weight.keys() if 145.0 <= l <= 150.0]
                    # print(f"位置02-2")
                    if valid_lengths:
                        self.weight_at_150mm = self.history_len_weight[max(valid_lengths)]
                        # print(f"位置02-3")
                
                # 如果仍然没有找到合适的重量，使用当前重量作为默认值
                if self.weight_at_150mm is None:
                    self.weight_at_150mm = current_weight
                    print(f"警告：未找到150mm附近的重量数据，使用当前重量 {current_weight} 作为默认值")

            # 确保weight_at_150mm有有效值
            if self.weight_at_150mm is not None:
                standard_weight_150 = self.weight_bin_dict.get(150, 0.0)
                delta = standard_weight_150 - self.weight_at_150mm
                # print(f"位置02-4")
                if delta >= self.lasu_adjust_step_weight:  # 使用配置的重量调整步长
                    adjustment = (delta / 0.1) * self.lasu_adjust_Increase  # 使用配置的拉速增加步长
                    new_correction = limit_range(adjustment)
                    # print(f"当前修正值: {new_correction}，位置03")
                else:
                    # 重量差不足，修正值保持为0
                    new_correction = 0.0
                    # print(f"当前修正值: {new_correction}，位置04")
            else:
                # 如果仍然无法获取有效重量，使用默认修正值0
                new_correction = 0.0
                print("警告：无法获取有效的150mm重量数据，修正值设为0")

        # ------------------------- 其他情况（如超过170mm） -------------------------
        else:
            new_correction = 0.0
            # print(f"当前修正值: {new_correction}，位置05")

        # 更新修正值
        self.lasu_correction = new_correction
        return self.lasu_correction

    def generate_weight_dict(self):
        """
        生成一个新的字典，键是长度(15-170)，值是重量
        计算规则：
        1. 使用self.prev_m作为0长度的初始重量
        2. 0-15长的重量使用self.history_len_weight中的实际重量
        3. 15-170长的重量计算方式：
           - 每个长度对应的重量 = 前一个长度的重量 + 该长度对应的重量改变量(从weight_bin_dict获取)
        """
        new_weight_dict = {}
        
        # 设置0长度的初始重量，保留两位小数
        new_weight_dict[0] = round(self.prev_m, 2)
        
        # 从history_len_weight获取0-15长的实际重量
        for length in range(1, 16):
            if length in self.history_len_weight:
                new_weight_dict[length] = round(self.history_len_weight[length], 2)
            else:
                # 如果没有该长度的实际重量，使用前一个长度的重量
                new_weight_dict[length] = round(new_weight_dict[length-1], 2)
        
        # 从15长开始，使用weight_bin_dict中的重量改变量计算后续长度的重量
        for length in range(15, 171):
            if length in self.weight_bin_dict:
                # 获取前15长的重量
                prev_15_weight = new_weight_dict[length-15]
                # 获取当前长度的重量改变量
                weight_change = self.weight_bin_dict[length]
                # 计算当前长度的重量，保留两位小数
                new_weight_dict[length] = round(prev_15_weight + weight_change, 2)
        
        return new_weight_dict
