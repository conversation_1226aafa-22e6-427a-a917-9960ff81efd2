"""
液口距校准v3版本测试模拟参数
生成用于测试的模拟数据参数
"""

import json
import random
import numpy as np

def generate_simulation_params():
    """
    生成液口距校准v3版本的模拟测试参数
    
    返回:
        dict: 包含所有必需参数的字典
    """
    
    # === 优化算法参数 ===
    
    # 1. 上一炉自动定埚位完成后的数据（10-12分钟的数据列表）
    # CCD温度：通常在1400-1500°C范围
    last_auto_ccd = [
        1445.2, 1446.8, 1444.5, 1447.1, 1445.9,
        1446.3, 1445.7, 1447.2, 1446.0, 1445.8
    ]
    
    # 绝对液口距：通常在15-25mm范围
    last_auto_yekouju = [
        18.5, 18.7, 18.3, 18.9, 18.6,
        18.4, 18.8, 18.2, 18.7, 18.5
    ]
    
    # 埚位值：通常在80-120mm范围（单个数值）
    last_auto_guowei = 95.3
    
    # 2. 上一炉引晶前的数据
    # CCD温度：引晶前温度通常比自动定埚位时高一些
    last_yinjing_ccd = [
        1449.1, 1450.3, 1448.9, 1449.7, 1449.5,
        1450.1, 1449.2, 1449.8, 1449.4, 1449.6
    ]
    
    # 绝对液口距：引晶前液口距通常会有所变化
    last_yinjing_yekouju = [
        20.1, 20.3, 19.9, 20.5, 20.2,
        20.0, 20.4, 19.8, 20.2, 20.1
    ]
    
    # 埚位值：引晶前埚位通常会调整
    last_yinjing_guowei = 98.7
    
    # 3. 本次自动定埚位完成后的数据
    # CCD温度：当前炉次的温度数据
    current_auto_ccd = [
        1443.8, 1444.2, 1443.5, 1444.6, 1443.9,
        1444.1, 1443.7, 1444.3, 1444.0, 1443.8
    ]
    
    # === 传统模型参数（用于回退） ===
    
    # 导流筒温度数据（通常在1400-1500°C范围）
    daoliutong_up = 1445.5
    daoliutong_down = 1443.2
    daoliutong_left = 1444.8
    daoliutong_right = 1444.1
    daoliutong_upleft = 1445.0
    daoliutong_upright = 1444.7
    
    # 定埚完成时的数据
    dingguo_finish_yewen = 1444.3  # 液温
    dingguo_finish_guowei = 95.3   # 埚位
    dingguo_finish_yekouju = 18.6  # 液口距
    
    # 构建完整的参数字典
    simulation_params = {
        # === 优化算法参数 ===
        "last_auto_ccd": last_auto_ccd,
        "last_auto_yekouju": last_auto_yekouju,
        "last_auto_guowei": last_auto_guowei,
        "last_yinjing_ccd": last_yinjing_ccd,
        "last_yinjing_yekouju": last_yinjing_yekouju,
        "last_yinjing_guowei": last_yinjing_guowei,
        "current_auto_ccd": current_auto_ccd,
        
        # === 传统模型参数 ===
        "daoliutong_up": daoliutong_up,
        "daoliutong_down": daoliutong_down,
        "daoliutong_left": daoliutong_left,
        "daoliutong_right": daoliutong_right,
        "daoliutong_upleft": daoliutong_upleft,
        "daoliutong_upright": daoliutong_upright,
        "dingguo_finish_yewen": dingguo_finish_yewen,
        "dingguo_finish_guowei": dingguo_finish_guowei,
        "dingguo_finish_yekouju": dingguo_finish_yekouju
    }
    
    return simulation_params

def generate_multiple_scenarios():
    """
    生成多种测试场景的参数
    
    返回:
        dict: 包含多种场景的参数字典
    """
    
    scenarios = {}
    
    # 场景1：正常情况 - 温度偏差较小
    scenarios["normal_case"] = generate_simulation_params()
    
    # 场景2：温度偏差较大的情况
    scenario2 = generate_simulation_params()
    # 本次温度明显偏低
    scenario2["current_auto_ccd"] = [
        1438.5, 1439.1, 1438.8, 1439.3, 1438.7,
        1439.0, 1438.6, 1439.2, 1438.9, 1438.8
    ]
    scenarios["large_temp_deviation"] = scenario2
    
    # 场景3：温度偏差很小的情况（测试除零保护）
    scenario3 = generate_simulation_params()
    # 本次温度与上次几乎相同
    scenario3["current_auto_ccd"] = [
        1445.1, 1445.3, 1445.0, 1445.2, 1445.1,
        1445.2, 1445.0, 1445.3, 1445.1, 1445.2
    ]
    scenarios["small_temp_deviation"] = scenario3
    
    # 场景4：缺少优化参数的情况（测试回退到传统模型）
    scenario4 = generate_simulation_params()
    # 移除部分优化参数，强制使用传统模型
    del scenario4["last_auto_ccd"]
    del scenario4["current_auto_ccd"]
    scenarios["fallback_to_traditional"] = scenario4
    
    # 场景5：包含异常数据的情况
    scenario5 = generate_simulation_params()
    # 在数据中加入一些异常值
    scenario5["last_auto_ccd"] = [
        1445.2, None, 1444.5, "invalid", 1445.9,
        1446.3, 1445.7, float('inf'), 1446.0, 1445.8
    ]
    scenario5["current_auto_ccd"] = [
        1443.8, 1444.2, float('nan'), 1444.6, 1443.9,
        1444.1, "error", 1444.3, 1444.0, 1443.8
    ]
    scenarios["with_invalid_data"] = scenario5
    
    return scenarios

def print_scenario_summary(scenario_name, params):
    """
    打印场景参数摘要
    
    参数:
        scenario_name (str): 场景名称
        params (dict): 参数字典
    """
    print(f"\n=== {scenario_name} ===")
    
    # 检查是否有优化参数
    has_optimization = all(key in params for key in [
        'last_auto_ccd', 'last_auto_yekouju', 'last_auto_guowei',
        'last_yinjing_ccd', 'last_yinjing_yekouju', 'last_yinjing_guowei',
        'current_auto_ccd'
    ])
    
    if has_optimization:
        print("✅ 包含完整优化算法参数")
        
        # 计算平均值（如果是列表）
        if isinstance(params.get('last_auto_ccd'), list):
            try:
                last_auto_avg = np.mean([x for x in params['last_auto_ccd'] if isinstance(x, (int, float)) and np.isfinite(x)])
                print(f"  上次自动定埚位CCD平均温度: {last_auto_avg:.2f}°C")
            except:
                print("  上次自动定埚位CCD温度: 包含异常数据")
        
        if isinstance(params.get('current_auto_ccd'), list):
            try:
                current_auto_avg = np.mean([x for x in params['current_auto_ccd'] if isinstance(x, (int, float)) and np.isfinite(x)])
                print(f"  本次自动定埚位CCD平均温度: {current_auto_avg:.2f}°C")
                
                # 计算温度偏差
                temp_deviation = 1449.75 - current_auto_avg  # 使用固定目标温度
                print(f"  预期温度偏差: {temp_deviation:.2f}°C")
            except:
                print("  本次自动定埚位CCD温度: 包含异常数据")
    else:
        print("⚠️ 优化参数不完整，将使用传统模型")
    
    # 检查传统模型参数
    traditional_features = ['daoliutong_up', 'daoliutong_down', 'daoliutong_left', 'daoliutong_right',
                           'daoliutong_upleft','daoliutong_upright', 'dingguo_finish_yewen',
                           'dingguo_finish_guowei','dingguo_finish_yekouju']
    
    has_traditional = all(key in params for key in traditional_features)
    if has_traditional:
        print("✅ 包含完整传统模型参数")
    else:
        print("⚠️ 传统模型参数不完整")

if __name__ == "__main__":
    print("液口距校准v3版本 - 模拟参数生成器")
    print("=" * 50)
    
    # 生成多种测试场景
    scenarios = generate_multiple_scenarios()
    
    # 打印所有场景的摘要
    for scenario_name, params in scenarios.items():
        print_scenario_summary(scenario_name, params)
    
    print(f"\n生成了 {len(scenarios)} 个测试场景")
    print("\n使用方法:")
    print("1. 选择需要的场景参数")
    print("2. 将参数传递给 predict_yekouju_adjust 函数")
    print("3. 观察预测结果和日志输出")
    
    # 保存参数到JSON文件（可选）
    try:
        with open('simulation_params.json', 'w', encoding='utf-8') as f:
            # 处理特殊值（NaN, Inf等）以便JSON序列化
            def clean_for_json(obj):
                if isinstance(obj, dict):
                    return {k: clean_for_json(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [clean_for_json(item) for item in obj]
                elif isinstance(obj, float):
                    if np.isnan(obj):
                        return "NaN"
                    elif np.isinf(obj):
                        return "Infinity" if obj > 0 else "-Infinity"
                    else:
                        return obj
                else:
                    return obj
            
            clean_scenarios = clean_for_json(scenarios)
            json.dump(clean_scenarios, f, indent=2, ensure_ascii=False)
        print("\n✅ 参数已保存到 simulation_params.json")
    except Exception as e:
        print(f"\n⚠️ 保存JSON文件失败: {e}")
